/**
 * 优化后的渲染核心组件示例
 */

import { defineComponent, ref, computed, toRef } from 'vue'
import { useApiEvents, type ApiExecutionContext } from '../composables/useApiEvents'
import type { ApiConfig } from '@neue-plus/services'

export default defineComponent({
  name: 'RenderCoreOptimized',
  props: {
    apis: {
      type: Object as () => Record<string, ApiConfig>,
      default: () => ({})
    },
    // 其他 props...
  },
  setup(props) {
    // API 配置引用
    const apiConfigs = computed(() => ({
      ...props.apis,
      // 可以添加默认的 API 配置
    }))

    // 使用优化后的 API 事件系统
    const {
      dynamicApiEvents,
      apiStates,
      cancelApi,
      clearCache,
      isApiLoading,
      getApiError
    } = useApiEvents(apiConfigs, {
      enableCache: true,
      cacheTimeout: 5 * 60 * 1000, // 5分钟缓存
      enableRetry: true,
      maxRetries: 3,
      onSuccess: (apiName, response) => {
        console.log(`✅ API ${apiName} executed successfully:`, response.data)
        // 可以在这里触发全局事件或更新状态
      },
      onError: (apiName, error) => {
        console.error(`❌ API ${apiName} failed:`, error.message)
        // 可以在这里显示错误提示
      }
    })

    // 示例：处理表单提交
    const handleFormSubmit = async (formData: any) => {
      const context: ApiExecutionContext = {
        ...formData,
        userId: 'current-user-id',
        timestamp: Date.now()
      }

      try {
        const result = await dynamicApiEvents.value.submitForm?.(context)
        if (result?.success) {
          // 处理成功逻辑
          console.log('Form submitted successfully')
        }
      } catch (error) {
        // 错误已经在 useApiEvents 中处理了
        console.log('Form submission handled with error recovery')
      }
    }

    // 示例：处理数据查询
    const handleDataQuery = async (queryParams: any) => {
      const context: ApiExecutionContext = {
        filter: queryParams.filter,
        pagination: queryParams.pagination,
        userId: 'current-user-id'
      }

      const result = await dynamicApiEvents.value.queryData?.(context)
      return result
    }

    // 示例：取消长时间运行的 API
    const handleCancelLongRunningApi = () => {
      cancelApi('longRunningApi')
    }

    return {
      // API 相关
      dynamicApiEvents,
      apiStates,
      isApiLoading,
      getApiError,
      
      // 业务方法
      handleFormSubmit,
      handleDataQuery,
      handleCancelLongRunningApi,
      
      // 工具方法
      clearCache
    }
  },
  
  render() {
    return (
      <div class="render-core">
        {/* 示例：显示 API 加载状态 */}
        {this.isApiLoading('submitForm') && (
          <div class="loading-indicator">
            正在提交表单...
            <button onClick={() => this.cancelApi('submitForm')}>
              取消
            </button>
          </div>
        )}
        
        {/* 示例：显示 API 错误 */}
        {this.getApiError('submitForm') && (
          <div class="error-message">
            提交失败: {this.getApiError('submitForm')}
            <button onClick={() => this.clearCache('submitForm')}>
              重试
            </button>
          </div>
        )}
        
        {/* 其他渲染内容... */}
      </div>
    )
  }
})
