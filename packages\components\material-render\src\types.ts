import { buildProp, buildProps } from '@neue-plus/utils'
import { ApiSchema, ElementEvent, SchemaElement } from '../types'
import type { ExtractPropTypes, PropType } from 'vue'

export const neMaterialRenderProps = buildProps({
  config: buildProp({
    type: Object as () => {
      configProvider?: Record<string, any>
      events?: ElementEvent[]
      api?: Record<string, any>
    },
    default: () => ({ configProvider: {}, events: [], api: {} }),
  }),
  elements: {
    type: Array as PropType<SchemaElement[]>,
    required: true,
  },
  events: {
    type: Object,
    default: () => {},
  },
  apis: {
    type: Object as () => Record<string, ApiSchema>,
    default: () => ({}),
  },
} as const)

export type NeMaterialRenderProps = ExtractPropTypes<
  typeof neMaterialRenderProps
>
// 事件类型
export type NeMaterialRenderEmits = {
  (e: 'beforeRender', node: any): void
  (e: 'afterRender', vnode: any, node: any): void
}
