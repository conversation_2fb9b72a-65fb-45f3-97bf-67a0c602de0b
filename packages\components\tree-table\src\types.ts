import { NeTableColumnProps, neTableProps } from '@neue-plus/components/table'
import { buildProps } from '@neue-plus/utils'
import type { TableColumnCtx, TableProps } from 'element-plus'
import type { ExtractPropTypes } from 'vue'

// 定义列配置类型，继承el-table-column的属性
export type NeTreeTableColumn = Partial<TableColumnCtx<any>> &
  NeTableColumnProps

// 只定义自定义属性，其他属性通过 v-bind="$attrs" 传递给 el-table
export const neTreeTableProps = buildProps({
  // 自定义属性：是否显示边框
  ...neTableProps,
  treeProps: {
    type: Object,
    default: () => ({
      children: 'children',
      label: 'label',
      hasChildren: 'hasChildren',
    }),
  },
} as const)

export type NeTreeTableProps = ExtractPropTypes<typeof neTreeTableProps> &
  TableProps<any>
