<template>
  <div class="widget-wrapper">
    <slot />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'

defineOptions({
  name: 'WidgetWrapper',
})
const innerRef = ref<any>(null)

function handleEvent(eventName: string, payload: any) {
  if (innerRef.value?.onCustomEvent) {
    innerRef.value.onCustomEvent(eventName, payload)
  }
}

onMounted(() => {
  console.log('WidgetWrapper mounted')
})
defineExpose({ handleEvent })
</script>
