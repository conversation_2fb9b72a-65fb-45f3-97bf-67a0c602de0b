/**
 * 表达式解析器
 * 负责解析模板字符串中的变量，支持 {{variable}} 语法
 */

import type { ExpressionParser, RequestContext } from './types'

/**
 * 默认的表达式解析器
 * 支持 {{variable}} 语法，可以访问嵌套属性
 */
export class DefaultExpressionParser implements ExpressionParser {
  /**
   * 解析模板中的表达式
   * @param template 模板对象或字符串
   * @param context 上下文对象
   * @returns 解析后的结果
   */
  parse(template: any, context: RequestContext = {}): any {
    if (typeof template === 'string') {
      return this.parseString(template, context)
    }

    if (Array.isArray(template)) {
      return template.map((item) => this.parse(item, context))
    }

    if (template && typeof template === 'object') {
      const result: any = {}
      for (const [key, value] of Object.entries(template)) {
        result[key] = this.parse(value, context)
      }
      return result
    }

    return template
  }

  /**
   * 解析字符串中的表达式
   * @param str 包含表达式的字符串
   * @param context 上下文对象
   * @returns 解析后的字符串或值
   */
  private parseString(str: string, context: RequestContext): any {
    // 匹配 {{expression}} 格式的表达式
    const expressionRegex = /\{\{([^}]+)\}\}/g

    // 如果整个字符串就是一个表达式，直接返回解析后的值
    const fullMatch = str.match(/^\{\{([^}]+)\}\}$/)
    if (fullMatch) {
      const expression = fullMatch[1].trim()
      return this.evaluateExpression(expression, context)
    }

    // 替换字符串中的所有表达式
    return str.replace(expressionRegex, (match, expression) => {
      const value = this.evaluateExpression(expression.trim(), context)
      return value !== undefined ? String(value) : match
    })
  }

  /**
   * 计算表达式的值
   * @param expression 表达式字符串
   * @param context 上下文对象
   * @returns 计算结果
   */
  private evaluateExpression(expression: string, context: RequestContext): any {
    try {
      // 支持点号访问嵌套属性，如 user.profile.name
      const keys = expression.split('.')
      let result = context

      for (const key of keys) {
        if (result && typeof result === 'object' && key in result) {
          result = result[key]
        } else {
          return undefined
        }
      }

      return result
    } catch (error) {
      console.warn(`Failed to evaluate expression: ${expression}`, error)
      return undefined
    }
  }
}

// 全局表达式解析器实例
let globalParser: ExpressionParser = new DefaultExpressionParser()

/**
 * 设置全局表达式解析器
 * @param parser 表达式解析器实例
 */
export function setExpressionParser(parser: ExpressionParser): void {
  globalParser = parser
}

/**
 * 获取全局表达式解析器
 * @returns 表达式解析器实例
 */
export function getExpressionParser(): ExpressionParser {
  return globalParser
}

/**
 * 解析表达式的便捷函数
 * @param template 模板对象或字符串
 * @param context 上下文对象
 * @returns 解析后的结果
 */
export function parseExpression(
  template: any,
  context: RequestContext = {}
): any {
  return globalParser.parse(template, context)
}

/**
 * 创建自定义表达式解析器
 * @param customEvaluator 自定义计算函数
 * @returns 表达式解析器实例
 */
export function createCustomParser(
  customEvaluator: (expression: string, context: RequestContext) => any
): ExpressionParser {
  return {
    parse(template: any, context: RequestContext = {}): any {
      if (typeof template === 'string') {
        const expressionRegex = /\{\{([^}]+)\}\}/g
        const fullMatch = template.match(/^\{\{([^}]+)\}\}$/)

        if (fullMatch) {
          return customEvaluator(fullMatch[1].trim(), context)
        }

        return template.replace(expressionRegex, (match, expression) => {
          const value = customEvaluator(expression.trim(), context)
          return value !== undefined ? String(value) : match
        })
      }

      if (Array.isArray(template)) {
        return template.map((item) => this.parse(item, context))
      }

      if (template && typeof template === 'object') {
        const result: any = {}
        for (const [key, value] of Object.entries(template)) {
          result[key] = this.parse(value, context)
        }
        return result
      }

      return template
    },
  }
}
