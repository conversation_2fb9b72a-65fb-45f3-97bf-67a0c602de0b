<template>
  <NeProTable
    :columns="proTableColumns"
    :data="allProTableData"
    :searchConfig="searchConfig"
    :request="request"
  />
</template>
<script setup lang="ts">
import type { ProTableColumn } from '@neue-plus/components/pro-table'
import { executeApi, quickStart } from '@neue-plus/services'
import { ref } from 'vue'
let searchConfig = ref({
  span: 8,
})
// 扩展数据用于ProTable测试
const allProTableData = ref([
  {
    name: '张三',
    age: 25,
    address: '北京市朝阳区',
    status: 'active',
    createTime: '2024-01-01',
  },
  {
    name: '李四',
    age: 30,
    address: '上海市浦东新区',
    status: 'inactive',
    createTime: '2024-01-02',
  },
  {
    name: '王五',
    age: 28,
    address: '广州市天河区',
    status: 'active',
    createTime: '2024-01-03',
  },
  {
    name: '赵六',
    age: 32,
    address: '深圳市南山区',
    status: 'pending',
    createTime: '2024-01-04',
  },
  {
    name: '钱七',
    age: 26,
    address: '杭州市西湖区',
    status: 'active',
    createTime: '2024-01-05',
  },
  {
    name: '孙八',
    age: 29,
    address: '南京市鼓楼区',
    status: 'inactive',
    createTime: '2024-01-06',
  },
  {
    name: '周九',
    age: 31,
    address: '武汉市洪山区',
    status: 'active',
    createTime: '2024-01-07',
  },
  {
    name: '吴十',
    age: 27,
    address: '成都市锦江区',
    status: 'pending',
    createTime: '2024-01-08',
  },
])
// ProTable列配置 - 包含筛选功能
const proTableColumns = ref<ProTableColumn[]>([
  {
    prop: 'FirstName',
    label: '姓名',
    width: 120,
    placeholder: '请输入姓名',
    fixed: 'left',
  },
  {
    prop: 'age',
    label: '年龄',
    width: 100,
    sortable: true,
    defaultValue: 10,
    align: 'center',
    valueType: 'digit',
  },
  {
    prop: 'status',
    label: '状态',
    width: 120,
    valueEnum: {
      active: {
        text: '激活',
      },
      inactive: {
        text: '未激活',
      },
      pending: {
        text: '待审核',
      },
    },
  },
  {
    prop: 'createTime',
    label: '创建时间',
    width: 150,
    valueType: 'dateRange',
  },
  {
    prop: 'LastName',
    label: '地址',
    showOverflowTooltip: true,
    minWidth: 200,
  },
])
const request = async (ctx: any) => {
  // 方式1：使用原有的 executeApi 方法
  let api = {
    url: 'https://services.odata.org/V4/TripPinServiceRW/People',
    method: 'get' as const,
    protocol: 'odata' as const,
    odata: {
      select: 'FirstName,LastName,Gender',
      top: 5,
    },
    dataPath: 'value',
  }
  console.log('executeApi params:', ctx)
  await quickStart()
  const res = await executeApi(api, ctx)
  console.log('executeApi result:', res)
  return res
} // OData 演示方法
</script>
