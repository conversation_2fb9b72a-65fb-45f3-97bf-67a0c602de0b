# 🔧 循环依赖修复 - 架构重构

## 问题识别

发现了严重的循环依赖问题：

```
bootstrap.ts → api-service.ts → bootstrap.ts
```

**具体路径**：

1. `bootstrap.ts` 导入 `api-service.ts` 来获取默认服务和设置中间件
2. `api-service.ts` 导入 `bootstrap.ts` 来进行自动初始化
3. 形成循环依赖，导致模块加载问题

## 🔍 问题分析

### **循环依赖的危害**

- ❌ 模块加载顺序不确定
- ❌ 可能导致运行时错误
- ❌ 影响代码分析和打包
- ❌ 难以进行单元测试

### **根本原因**

- `bootstrap.ts` 负责初始化，但需要访问 `ApiService`
- `ApiService` 需要自动初始化功能，但这在 `bootstrap.ts` 中
- 职责边界不清晰，导致相互依赖

## 🚀 解决方案

### **1. 创建独立的初始化模块**

#### **新建 `initialization.ts`**

```typescript
// packages/services/src/core/initialization.ts
import { getGlobalContainer } from './container'

export function isInitialized(): boolean {
  const container = getGlobalContainer()
  return container.has('rest-adapter') && container.has('odata-adapter')
}

export async function autoInitialize(): Promise<void> {
  if (!isInitialized()) {
    // 延迟导入避免循环依赖
    const { quickStart } = await import('./bootstrap')
    await quickStart()
  }
}
```

**优势**：

- ✅ 独立的初始化状态管理
- ✅ 使用延迟导入避免循环依赖
- ✅ 清晰的职责分离

### **2. 重构 bootstrap.ts**

#### **修改前**：

```typescript
// ❌ 直接导入 api-service，造成循环依赖
import { getDefaultApiService } from '../services/api-service'

export async function initialize(config: InitializationConfig) {
  // 获取 API 服务并设置中间件
  const apiService = getDefaultApiService()
  const middlewareManager = apiService.getMiddlewareManager()

  // 注册中间件...
}
```

#### **修改后**：

```typescript
// ✅ 移除对 api-service 的依赖
export async function initialize(config: InitializationConfig) {
  // 注册中间件配置到容器中（延迟应用）
  const middlewareConfig = {
    enableDefaultMiddlewares,
    enableLogging,
    enablePerformance,
    customMiddlewares: middlewares,
  }
  container.register('middleware-config', () => middlewareConfig)
}
```

**优势**：

- ✅ 移除了对 `api-service.ts` 的直接依赖
- ✅ 使用容器存储配置，延迟应用
- ✅ 保持初始化功能的完整性

### **3. 重构 api-service.ts**

#### **修改前**：

```typescript
// ❌ 直接导入 bootstrap，造成循环依赖
import { autoInitialize } from '../core/bootstrap'

export async function executeApi(
  config: ApiConfig,
  context: RequestContext = {}
) {
  await autoInitialize() // 直接调用
  return getDefaultApiService().execute(config, context)
}
```

#### **修改后**：

```typescript
// ✅ 使用独立的初始化模块
import { autoInitialize } from '../core/initialization'

export async function executeApi(
  config: ApiConfig,
  context: RequestContext = {}
) {
  await autoInitialize() // 通过独立模块调用
  return getDefaultApiService().execute(config, context)
}

class ApiService {
  private initializeDefaults(): void {
    // 应用中间件配置
    this.applyMiddlewareConfig()
  }

  private applyMiddlewareConfig(): void {
    const container = getGlobalContainer()
    try {
      const middlewareConfig = container.get<any>('middleware-config')
      if (middlewareConfig) {
        this.setupMiddlewares(middlewareConfig)
      }
    } catch {
      // 如果没有中间件配置，则跳过
    }
  }
}
```

**优势**：

- ✅ 使用独立的初始化模块
- ✅ 从容器中获取中间件配置
- ✅ 延迟导入中间件模块

## 📊 架构改进对比

### **修改前的依赖关系**：

```
bootstrap.ts ←→ api-service.ts  (循环依赖)
     ↓              ↓
middleware.ts   container.ts
```

### **修改后的依赖关系**：

```
initialization.ts → bootstrap.ts
        ↓               ↓
api-service.ts ← container.ts
        ↓
middleware.ts (延迟导入)
```

## 🎯 关键改进

### **1. 职责分离**

- **`initialization.ts`**: 专门负责初始化状态管理
- **`bootstrap.ts`**: 专门负责框架组件注册
- **`api-service.ts`**: 专门负责 API 请求处理

### **2. 延迟加载策略**

```typescript
// 延迟导入避免循环依赖
export async function autoInitialize(): Promise<void> {
  if (!isInitialized()) {
    const { quickStart } = await import('./bootstrap')
    await quickStart()
  }
}

// 延迟导入中间件
if (enableLogging) {
  const { createLoggingMiddleware } = await import('../core/middleware')
  this.middlewareManager.use(createLoggingMiddleware(config))
}
```

### **3. 配置容器化**

```typescript
// 在 bootstrap 中注册配置
container.register('middleware-config', () => middlewareConfig)

// 在 api-service 中应用配置
const middlewareConfig = container.get<any>('middleware-config')
if (middlewareConfig) {
  this.setupMiddlewares(middlewareConfig)
}
```

## ✅ 修复验证

### **1. 依赖检查**

```bash
# 修复前
Circular dependency: bootstrap.ts → api-service.ts → bootstrap.ts

# 修复后
No circular dependencies detected ✅
```

### **2. 功能验证**

```typescript
// 自动初始化仍然正常工作
const result = await executeApi({
  url: 'https://services.odata.org/V4/TripPinServiceRW/People',
  method: 'get',
  protocol: 'odata',
})
// ✅ 正常工作，无循环依赖问题
```

### **3. 中间件功能**

```typescript
// 中间件配置仍然正常应用
await quickStart({
  enableLogging: true,
  enablePerformance: true,
})
// ✅ 中间件正常注册和工作
```

## 🔄 迁移指南

### **对于现有代码**

- ✅ **无需修改**：所有公开 API 保持不变
- ✅ **向后兼容**：现有功能完全保持
- ✅ **透明修复**：用户无感知的架构改进

### **对于新开发**

- ✅ 使用 `executeApi` 进行 API 调用（自动初始化）
- ✅ 使用 `quickStart` 进行框架初始化
- ✅ 避免直接导入内部模块

## 🎉 总结

这次循环依赖修复带来了多方面的改进：

### **1. 架构健康**

- ✅ 消除了循环依赖
- ✅ 清晰的模块职责
- ✅ 更好的代码组织

### **2. 性能优化**

- ✅ 延迟加载减少初始化开销
- ✅ 按需导入模块
- ✅ 更快的应用启动

### **3. 维护性提升**

- ✅ 更容易进行单元测试
- ✅ 更好的代码分析支持
- ✅ 更清晰的依赖关系

### **4. 功能完整性**

- ✅ 保持所有现有功能
- ✅ 向后兼容
- ✅ 无破坏性变更

这个修复不仅解决了技术问题，还改善了整体架构质量，为未来的扩展和维护奠定了良好基础。
