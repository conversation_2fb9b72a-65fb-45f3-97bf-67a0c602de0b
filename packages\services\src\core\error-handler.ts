/**
 * 错误处理模块
 * 提供统一的错误处理机制
 */

import type { ErrorHandler } from './types'

/**
 * 服务错误类
 */
export class ServiceError extends Error {
  public readonly code: string
  public readonly statusCode?: number
  public readonly originalError?: Error

  constructor(
    message: string,
    code: string = 'UNKNOWN_ERROR',
    statusCode?: number,
    originalError?: Error
  ) {
    super(message)
    this.name = 'ServiceError'
    this.code = code
    this.statusCode = statusCode
    this.originalError = originalError
  }
}

/**
 * 网络错误类
 */
export class NetworkError extends ServiceError {
  constructor(message: string, statusCode?: number, originalError?: Error) {
    super(message, 'NETWORK_ERROR', statusCode, originalError)
    this.name = 'NetworkError'
  }
}

/**
 * 认证错误类
 */
export class AuthenticationError extends ServiceError {
  constructor(
    message: string = 'Authentication failed',
    originalError?: Error
  ) {
    super(message, 'AUTHENTICATION_ERROR', 401, originalError)
    this.name = 'AuthenticationError'
  }
}

/**
 * 授权错误类
 */
export class AuthorizationError extends ServiceError {
  constructor(message: string = 'Access denied', originalError?: Error) {
    super(message, 'AUTHORIZATION_ERROR', 403, originalError)
    this.name = 'AuthorizationError'
  }
}

/**
 * 验证错误类
 */
export class ValidationError extends ServiceError {
  public readonly field?: string

  constructor(message: string, field?: string, originalError?: Error) {
    super(message, 'VALIDATION_ERROR', 400, originalError)
    this.name = 'ValidationError'
    this.field = field
  }
}

/**
 * 超时错误类
 */
export class TimeoutError extends ServiceError {
  constructor(message: string = 'Request timeout', originalError?: Error) {
    super(message, 'TIMEOUT_ERROR', 408, originalError)
    this.name = 'TimeoutError'
  }
}

/**
 * 默认错误处理器
 */
const defaultErrorHandler: ErrorHandler = (message: string) => {
  console.error('[Services Error]:', message)
}

// 全局错误处理器
let globalErrorHandler: ErrorHandler = defaultErrorHandler

/**
 * 设置全局错误处理器
 * @param handler 错误处理函数
 */
export function setErrorHandler(handler: ErrorHandler): void {
  globalErrorHandler = handler
}

/**
 * 获取全局错误处理器
 * @returns 错误处理函数
 */
export function getErrorHandler(): ErrorHandler {
  return globalErrorHandler
}

/**
 * 处理错误的便捷函数
 * @param error 错误对象或消息
 */
export function handleError(error: Error | string): void {
  const message = error instanceof Error ? error.message : error

  // 尝试使用 Element Plus 的消息组件
  try {
    const { ElMessage } = require('element-plus')
    if (ElMessage && typeof ElMessage.error === 'function') {
      ElMessage.error(message)
      return
    }
  } catch {
    // Element Plus 不可用，使用全局错误处理器
  }

  globalErrorHandler(message)
}

/**
 * 从 HTTP 响应创建错误
 * @param response HTTP 响应对象
 * @returns 服务错误实例
 */
export function createErrorFromResponse(response: {
  status: number
  statusText: string
  data?: any
}): ServiceError {
  const { status, statusText, data } = response
  const message = data?.message || data?.error || statusText || 'Request failed'

  switch (status) {
    case 400:
      return new ValidationError(message)
    case 401:
      return new AuthenticationError(message)
    case 403:
      return new AuthorizationError(message)
    case 408:
      return new TimeoutError(message)
    case 404:
      return new ServiceError(
        `Resource not found: ${message}`,
        'NOT_FOUND',
        status
      )
    case 500:
      return new ServiceError(
        `Server error: ${message}`,
        'SERVER_ERROR',
        status
      )
    default:
      return new NetworkError(message, status)
  }
}

/**
 * 从通用错误创建服务错误
 * @param error 原始错误
 * @returns 服务错误实例
 */
export function createServiceError(error: any): ServiceError {
  if (error instanceof ServiceError) {
    return error
  }

  if (error?.response) {
    // Axios 错误格式
    return createErrorFromResponse({
      status: error.response.status,
      statusText: error.response.statusText,
      data: error.response.data,
    })
  }

  if (error?.request) {
    // 网络错误
    return new NetworkError('Network error: No response received')
  }

  // 其他错误
  const message = error?.message || 'Unknown error occurred'
  return new ServiceError(message, 'UNKNOWN_ERROR', undefined, error)
}

/**
 * 错误重试装饰器
 * @param maxRetries 最大重试次数
 * @param delay 重试延迟（毫秒）
 * @returns 装饰器函数
 */
export function withRetry(maxRetries: number = 3, delay: number = 1000) {
  return function <T extends (...args: any[]) => Promise<any>>(
    target: any,
    propertyKey: string,
    descriptor: TypedPropertyDescriptor<T>
  ) {
    const originalMethod = descriptor.value!

    descriptor.value = async function (this: any, ...args: any[]) {
      let lastError: Error

      for (let attempt = 0; attempt <= maxRetries; attempt++) {
        try {
          return await originalMethod.apply(this, args)
        } catch (error) {
          lastError = error as Error

          if (attempt === maxRetries) {
            break
          }

          // 只对网络错误进行重试
          if (error instanceof NetworkError || error instanceof TimeoutError) {
            await new Promise((resolve) =>
              setTimeout(resolve, delay * 2 ** attempt)
            )
            continue
          }

          // 其他错误不重试
          break
        }
      }

      throw lastError!
    } as T

    return descriptor
  }
}
