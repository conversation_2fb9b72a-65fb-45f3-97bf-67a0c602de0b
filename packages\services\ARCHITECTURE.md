# Services 架构设计文档

## 概述

`@neue-plus/services` 经过全面重构，采用了现代化的架构设计，提供了更清晰的模块化结构、更强大的功能和更好的可维护性。

## 设计原则

### 1. 单一职责原则 (SRP)

每个模块只负责一个特定的功能：

- `expression-parser`: 只负责表达式解析
- `error-handler`: 只负责错误处理
- `http-client`: 只负责 HTTP 请求
- `odata-client`: 只负责 OData 协议处理

### 2. 开放封闭原则 (OCP)

通过接口和适配器模式，支持扩展而不修改现有代码：

- `ApiAdapter` 接口支持新的协议适配器
- `ExpressionParser` 接口支持自定义解析器
- `ErrorHandler` 支持自定义错误处理

### 3. 依赖倒置原则 (DIP)

高层模块不依赖低层模块，都依赖于抽象：

- `ApiService` 依赖 `ApiAdapter` 接口
- `ODataClient` 依赖 `HttpClient` 接口
- 支持依赖注入和配置

### 4. 接口隔离原则 (ISP)

提供细粒度的接口，客户端只依赖它们需要的接口：

- `IODataClient` 接口只包含 OData 相关方法
- `ApiClient` 接口只包含基础 HTTP 方法
- `ExpressionParser` 接口只包含解析方法

## 架构层次

```
┌─────────────────────────────────────────┐
│                应用层                    │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │ executeApi  │  │ createODataClient│   │
│  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│                服务层                    │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │ ApiService  │  │ ClientFactory   │   │
│  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│               适配器层                   │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │RestAdapter  │  │ ODataAdapter    │   │
│  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│               客户端层                   │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │ HttpClient  │  │ ODataClient     │   │
│  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│                核心层                    │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │ExpressionParser│ │ ErrorHandler   │   │
│  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────┘
```

## 模块详解

### 核心层 (Core)

#### types.ts

- **职责**: 定义所有的类型接口和类型别名
- **特点**:
  - 统一的类型定义
  - 向后兼容的类型别名
  - 清晰的接口分离

#### expression-parser.ts

- **职责**: 解析模板字符串中的变量表达式
- **特点**:
  - 支持 `{{variable}}` 语法
  - 支持嵌套属性访问
  - 可扩展的解析器接口
  - 全局和自定义解析器支持

#### error-handler.ts

- **职责**: 统一的错误处理机制
- **特点**:
  - 多种错误类型定义
  - 全局错误处理器
  - 错误重试装饰器
  - Element Plus 集成

### 客户端层 (Clients)

#### http-client.ts

- **职责**: 基础的 HTTP 请求功能
- **特点**:
  - 基于 Axios 的封装
  - 请求/响应拦截器
  - 统一的错误处理
  - 认证头部管理

#### odata-client.ts

- **职责**: OData 协议的完整实现
- **特点**:
  - 完整的 CRUD 操作
  - OData 查询选项支持
  - 函数和操作调用
  - 认证支持
  - 元数据获取

### 适配器层 (Adapters)

#### rest-adapter.ts

- **职责**: 处理标准 REST API 请求
- **特点**:
  - 表达式解析集成
  - 数据路径提取
  - 统一响应格式

#### odata-adapter.ts

- **职责**: 处理 OData 协议请求
- **特点**:
  - 客户端缓存管理
  - 智能方法路由
  - URL 解析和构建

### 服务层 (Services)

#### api-service.ts

- **职责**: 统一的 API 调用服务
- **特点**:
  - 适配器模式实现
  - 配置标准化
  - 批量和并发请求支持
  - 向后兼容处理

### 工厂层 (Factories)

#### client-factory.ts

- **职责**: 客户端实例的创建和管理
- **特点**:
  - 客户端缓存
  - 默认配置管理
  - 批量创建支持
  - 缓存统计

## 数据流

### 请求流程

```
用户调用 executeApi()
        ↓
ApiService.execute()
        ↓
查找合适的 Adapter
        ↓
Adapter.execute()
        ↓
创建/获取 Client
        ↓
Client.request()
        ↓
HttpClient.request()
        ↓
Axios 请求
        ↓
响应处理和错误处理
        ↓
返回统一格式的响应
```

### 配置标准化流程

```
用户配置 (LegacyApiSchema)
        ↓
ApiService.normalizeConfig()
        ↓
标准化为 RestApiConfig 或 ODataApiConfig
        ↓
传递给相应的 Adapter
        ↓
Adapter 处理具体协议逻辑
```

## 扩展点

### 1. 自定义适配器

```typescript
class CustomAdapter implements ApiAdapter {
  canHandle(config: ApiConfig): boolean {
    return config.protocol === 'custom'
  }

  async execute(
    config: ApiConfig,
    context: RequestContext
  ): Promise<ApiResponse> {
    // 自定义协议处理逻辑
  }
}

// 注册适配器
const apiService = createApiService()
apiService.registerAdapter(new CustomAdapter())
```

### 2. 自定义表达式解析器

```typescript
const customParser = createCustomParser((expression, context) => {
  // 自定义解析逻辑
  return customEvaluate(expression, context)
})

setExpressionParser(customParser)
```

### 3. 自定义错误处理

```typescript
setErrorHandler((message) => {
  // 集成到自定义日志系统
  myLogger.error(message)

  // 显示自定义通知
  showCustomNotification(message)
})
```

## 性能优化

### 1. 客户端缓存

- OData 客户端按 baseUrl 缓存
- HTTP 客户端按配置缓存
- 避免重复创建实例

### 2. 请求优化

- 支持批量请求
- 支持并发控制
- 自动重试机制

### 3. 内存管理

- 提供缓存清理方法
- 缓存统计和监控
- 合理的缓存策略

## 测试策略

### 1. 单元测试

- 每个模块独立测试
- Mock 外部依赖
- 覆盖核心逻辑

### 2. 集成测试

- 适配器和客户端集成
- 端到端请求流程
- 错误处理流程

### 3. 性能测试

- 客户端缓存效果
- 并发请求性能
- 内存使用情况

## 向后兼容

### 1. API 兼容

- 保持 `executeApi` 函数签名
- 保持 `createODataClient` 函数
- 保持主要类型定义

### 2. 行为兼容

- 相同的响应格式
- 相同的错误处理方式
- 相同的表达式解析语法

### 3. 配置兼容

- 支持旧的配置格式
- 自动转换为新格式
- 渐进式迁移支持

## 未来规划

### 1. 协议支持

- GraphQL 适配器
- WebSocket 支持
- gRPC 支持

### 2. 功能增强

- 请求缓存机制
- 离线支持
- 请求队列管理

### 3. 开发体验

- 更好的 TypeScript 支持
- 开发工具集成
- 调试和监控工具

这个新架构为 `@neue-plus/services` 提供了坚实的基础，支持未来的扩展和演进，同时保持了良好的向后兼容性。
