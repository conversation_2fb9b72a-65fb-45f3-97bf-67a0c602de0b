<template>
  <widget-wrapper>
    <el-dialog
      v-model="dialogVisible"
      :title="props.config.title || '业务类型'"
      width="500"
      :before-close="handleClose"
    >
      <el-tree
        style="max-width: 600px"
        :data="data"
        :props="defaultProps"
        @node-click="handleNodeClick"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="dialogVisible = false">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </widget-wrapper>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import WidgetWrapper from '@neue-plus/components/widget-wrapper'
import type { PropType } from 'vue'
import type { BusinessTypeConfig } from './business-type'

defineOptions({
  name: 'BusinessType',
  inheritAttrs: false,
})

const props = defineProps({
  config: {
    type: Object as PropType<BusinessTypeConfig>,
    required: true,
  },
})

const dialogVisible = ref(false)

const handleNodeClick = () => {
  dialogVisible.value = true
}
const handleClose = (done: () => void) => {
  dialogVisible.value = false
  done()
}

interface Tree {
  label: string
  children?: Tree[]
}
const data: Tree[] = [
  {
    label: 'Level one 1',
    children: [
      {
        label: 'Level two 1-1',
        children: [
          {
            label: 'Level three 1-1-1',
          },
        ],
      },
    ],
  },
  {
    label: 'Level one 2',
    children: [
      {
        label: 'Level two 2-1',
        children: [
          {
            label: 'Level three 2-1-1',
          },
        ],
      },
      {
        label: 'Level two 2-2',
        children: [
          {
            label: 'Level three 2-2-1',
          },
        ],
      },
    ],
  },
]
const defaultProps = {
  children: 'children',
  label: 'label',
}
</script>
