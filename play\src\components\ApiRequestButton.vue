<template>
  <div class="api-request-button">
    <el-card class="request-card">
      <template #header>
        <div class="card-header">
          <h4>🚀 API 请求组件</h4>
          <el-tag :type="getMethodTagType(selectedMethod)">{{
            selectedMethod.toUpperCase()
          }}</el-tag>
        </div>
      </template>

      <el-space direction="vertical" style="width: 100%">
        <!-- 请求配置 -->
        <div class="request-config">
          <el-row :gutter="16">
            <el-col :span="6">
              <el-select
                v-model="selectedMethod"
                placeholder="选择请求方法"
                style="width: 100%"
              >
                <el-option
                  v-for="method in httpMethods"
                  :key="method.value"
                  :label="method.label"
                  :value="method.value"
                  :disabled="method.disabled"
                >
                  <span :style="{ color: method.color }">{{
                    method.label
                  }}</span>
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="12">
              <el-input
                v-model="requestUrl"
                placeholder="请输入 API 地址"
                clearable
              >
                <template #prepend>URL</template>
              </el-input>
            </el-col>
            <el-col :span="6">
              <el-select
                v-model="protocol"
                placeholder="协议"
                style="width: 100%"
              >
                <el-option label="REST" value="rest" />
                <el-option label="OData" value="odata" />
              </el-select>
            </el-col>
          </el-row>
        </div>

        <!-- 请求参数 -->
        <div class="request-params">
          <el-tabs v-model="activeTab">
            <el-tab-pane label="Headers" name="headers">
              <div class="params-editor">
                <el-button
                  size="small"
                  @click="addHeader"
                  style="margin-bottom: 8px"
                >
                  添加 Header
                </el-button>
                <div
                  v-for="(header, index) in headers"
                  :key="index"
                  class="param-row"
                >
                  <el-input
                    v-model="header.key"
                    placeholder="Header 名称"
                    style="width: 40%"
                  />
                  <el-input
                    v-model="header.value"
                    placeholder="Header 值"
                    style="width: 40%; margin-left: 8px"
                  />
                  <el-button
                    size="small"
                    type="danger"
                    @click="removeHeader(index)"
                    style="margin-left: 8px"
                  >
                    删除
                  </el-button>
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="Query Params" name="params">
              <div class="params-editor">
                <el-button
                  size="small"
                  @click="addParam"
                  style="margin-bottom: 8px"
                >
                  添加参数
                </el-button>
                <div
                  v-for="(param, index) in params"
                  :key="index"
                  class="param-row"
                >
                  <el-input
                    v-model="param.key"
                    placeholder="参数名"
                    style="width: 40%"
                  />
                  <el-input
                    v-model="param.value"
                    placeholder="参数值"
                    style="width: 40%; margin-left: 8px"
                  />
                  <el-button
                    size="small"
                    type="danger"
                    @click="removeParam(index)"
                    style="margin-left: 8px"
                  >
                    删除
                  </el-button>
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="Body" name="body" v-if="needsBody">
              <el-input
                v-model="requestBody"
                type="textarea"
                :rows="6"
                placeholder="请输入 JSON 格式的请求体"
              />
              <div style="margin-top: 8px">
                <el-button size="small" @click="formatJson"
                  >格式化 JSON</el-button
                >
                <el-button size="small" @click="clearBody">清空</el-button>
              </div>
            </el-tab-pane>

            <el-tab-pane label="OData" name="odata" v-if="protocol === 'odata'">
              <el-alert
                title="OData 语法提示"
                type="info"
                :closable="false"
                show-icon
                style="margin-bottom: 16px"
              >
                <ul style="margin: 0; padding-left: 20px">
                  <li>字符串值使用单引号: <code>Gender eq 'Female'</code></li>
                  <li>数值不需要引号: <code>Age gt 18</code></li>
                  <li>
                    多个字段用逗号分隔: <code>FirstName,LastName,Gender</code>
                  </li>
                </ul>
              </el-alert>

              <el-row :gutter="16">
                <el-col :span="12">
                  <el-input
                    v-model="odataOptions.select"
                    placeholder="FirstName,LastName,Gender"
                  >
                    <template #prepend>$select</template>
                  </el-input>
                </el-col>
                <el-col :span="12">
                  <el-input
                    v-model="odataOptions.filter"
                    placeholder="Gender eq 'Female'"
                  >
                    <template #prepend>$filter</template>
                  </el-input>
                </el-col>
              </el-row>
              <el-row :gutter="16" style="margin-top: 8px">
                <el-col :span="8">
                  <el-input-number
                    v-model="odataOptions.top"
                    placeholder="$top"
                    :min="1"
                    :max="1000"
                    style="width: 100%"
                  />
                </el-col>
                <el-col :span="8">
                  <el-input-number
                    v-model="odataOptions.skip"
                    placeholder="$skip"
                    :min="0"
                    style="width: 100%"
                  />
                </el-col>
                <el-col :span="8">
                  <el-input
                    v-model="odataOptions.orderby"
                    placeholder="$orderby"
                  >
                    <template #prepend>$orderby</template>
                  </el-input>
                </el-col>
              </el-row>
            </el-tab-pane>
          </el-tabs>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <el-button
            type="primary"
            :loading="loading"
            @click="sendRequest"
            :disabled="!requestUrl"
          >
            发送请求
          </el-button>
          <el-button @click="clearAll">清空所有</el-button>
          <el-button @click="loadExample">加载示例</el-button>
        </div>

        <!-- 响应结果 -->
        <div v-if="response || error" class="response-section">
          <el-divider>响应结果</el-divider>

          <div v-if="error" class="error-response">
            <el-alert
              :title="error"
              type="error"
              show-icon
              :closable="true"
              @close="error = ''"
            />
          </div>

          <div v-if="response" class="success-response">
            <div class="response-info">
              <el-tag type="success">请求成功</el-tag>
              <span class="response-time">耗时: {{ responseTime }}ms</span>
              <span v-if="response.total" class="response-count">
                总计: {{ response.total }} 条
              </span>
            </div>

            <el-collapse v-model="responseCollapse">
              <el-collapse-item title="查看响应数据" name="data">
                <pre class="response-data">{{
                  JSON.stringify(response, null, 2)
                }}</pre>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
      </el-space>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue'
import { createODataClient, executeApi, quickStart } from '@neue-plus/services'
import { ElMessage } from 'element-plus'
import { keys, values } from 'lodash-unified'

// 响应式数据
const loading = ref(false)
const selectedMethod = ref('get')
const requestUrl = ref('')
const protocol = ref('rest')
const activeTab = ref('headers')
const requestBody = ref('')
const response = ref()
const error = ref('')
const responseTime = ref(0)
const responseCollapse = ref(['data'])

// HTTP 方法配置
const httpMethods = [
  { label: 'GET', value: 'get', color: '#67C23A', disabled: false },
  { label: 'POST', value: 'post', color: '#409EFF', disabled: false },
  { label: 'PUT', value: 'put', color: '#E6A23C', disabled: false },
  { label: 'PATCH', value: 'patch', color: '#F56C6C', disabled: false },
  { label: 'DELETE', value: 'delete', color: '#F56C6C', disabled: false },
]

interface Item {
  key: string
  value: string
}
// 参数数组
const headers = ref<Item[]>([
  { key: 'Content-Type', value: 'application/json' },
])
const params = ref<Item[]>([])

// OData 选项
const odataOptions = reactive({
  select: '',
  filter: '',
  top: 0,
  skip: null,
  orderby: '',
})

// 计算属性
const needsBody = computed(() => {
  return ['post', 'put', 'patch'].includes(selectedMethod.value)
})

// 方法
const getMethodTagType = (method: string) => {
  const typeMap: any = {
    get: 'success',
    post: 'primary',
    put: 'warning',
    patch: 'info',
    delete: 'danger',
  }
  return typeMap[method] || 'info'
}

const addHeader = () => {
  headers.value.push({ key: '', value: '' })
}

const removeHeader = (index: number) => {
  headers.value.splice(index, 1)
}

const addParam = () => {
  params.value.push({ key: '', value: '' })
}

const removeParam = (index: number) => {
  params.value.splice(index, 1)
}

const formatJson = () => {
  try {
    const parsed = JSON.parse(requestBody.value)
    requestBody.value = JSON.stringify(parsed, null, 2)
    ElMessage.success('JSON 格式化成功')
  } catch (err) {
    ElMessage.error('JSON 格式错误')
  }
}

const clearBody = () => {
  requestBody.value = ''
}

const clearAll = () => {
  requestUrl.value = ''
  requestBody.value = ''
  headers.value = [{ key: 'Content-Type', value: 'application/json' }]
  params.value = []
  response.value = null
  error.value = ''
  Object.assign(odataOptions, {
    select: '',
    filter: '',
    top: 0,
    skip: null,
    orderby: '',
  })
}

const loadExample = () => {
  if (protocol.value === 'odata') {
    requestUrl.value = 'https://services.odata.org/V4/TripPinServiceRW/People'
    odataOptions.select = 'FirstName,LastName,Gender'
    odataOptions.filter = ''
    odataOptions.top = 5
  } else {
    requestUrl.value = 'https://jsonplaceholder.typicode.com/posts'
    if (selectedMethod.value === 'post') {
      requestBody.value = JSON.stringify(
        {
          title: 'foo',
          body: 'bar',
          userId: 1,
        },
        null,
        2
      )
    }
  }
  ElMessage.success('示例数据已加载')
}

const sendRequest = async () => {
  if (!requestUrl.value) {
    ElMessage.error('请输入 API 地址')
    return
  }

  loading.value = true
  error.value = ''
  response.value = null
  const startTime = Date.now()

  try {
    // 构建请求头
    const requestHeaders: any = keys(headers.value).reduce((acc, key) => {
      acc[key] = headers.value[key].value
      return acc
    }, {})

    // 构建查询参数
    const queryParams: any = keys(params.value).reduce((acc, key) => {
      acc[key] = params.value[key].value
      return acc
    }, {})

    // 构建请求配置
    const config: any = {
      url: requestUrl.value,
      method: selectedMethod.value,
      protocol: protocol.value,
      headers: requestHeaders,
      params: queryParams,
      query: odataOptions,
    }
    let context = undefined
    if (protocol.value === 'odata') {
      if (selectedMethod.value === 'post') {
        context = {
          UserName: 'russellwhyte',
          FirstName: 'Russell' + Math.ceil(Math.random() * 100),
          LastName: 'Whyte',
          Emails: ['<EMAIL>', '<EMAIL>'],
          AddressInfo: [
            {
              Address: '187 Suffolk Ln.',
              City: {
                CountryRegion: 'United States',
                Name: 'Boise',
                Region: 'ID',
              },
            },
          ],
          Gender: 'Male',
          Concurrency: 638895380328519200,
        }
      } else if (selectedMethod.value === 'patch') {
        context = {
          UserName: 'russellwhyte',
        }
      } else if (selectedMethod.value === 'put') {
        context = {
          UserName: 'russellwhyte',
          FirstName: 'Russell',
        }
      }
    }
    // await quickStart()
    const result = await executeApi(config, context)
    console.log(result)

    responseTime.value = Date.now() - startTime
    response.value = result

    ElMessage.success('请求发送成功')
  } catch (err: any) {
    responseTime.value = Date.now() - startTime
    error.value = err.message || '请求失败'
    console.log(err)
    ElMessage.error('请求失败: ' + error.value)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.api-request-button {
  margin: 20px 0;
}

.request-card {
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h4 {
  margin: 0;
  color: #303133;
}

.request-config {
  margin-bottom: 16px;
}

.request-params {
  margin-bottom: 16px;
}

.params-editor {
  min-height: 100px;
}

.param-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.action-buttons {
  text-align: center;
  margin: 16px 0;
}

.response-section {
  margin-top: 16px;
}

.response-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.response-time {
  color: #909399;
  font-size: 14px;
}

.response-count {
  color: #67c23a;
  font-size: 14px;
  font-weight: 600;
}

.response-data {
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  color: #2c3e50;
  overflow-x: auto;
  max-height: 400px;
  overflow-y: auto;
}

.error-response {
  margin-bottom: 16px;
}

.success-response {
  margin-bottom: 16px;
}
</style>
