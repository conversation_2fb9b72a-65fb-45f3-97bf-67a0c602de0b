/**
 * 工具函数
 */

import {
  ApiConfig,
  ApiResponse,
  ODataApiConfig,
  RequestContext,
  RestApiConfig,
  ServiceError,
} from '../types'
import { getDefaultAxiosClient } from '../clients/axios-client'
import { createODataClient } from '../clients/odata-client'

/**
 * 统一的 API 执行函数
 */
export async function executeApi(
  config: ApiConfig,
  context: RequestContext = {}
): Promise<ApiResponse> {
  try {
    console.log('[executeApi] 执行 API 请求:', config)

    // 根据协议类型选择对应的客户端
    if (config.protocol === 'odata') {
      return await executeODataApi(config as ODataApiConfig, context)
    } else {
      return await executeRestApi(config as RestApiConfig, context)
    }
  } catch (error) {
    console.error('[executeApi] API 请求失败:', error)

    if (error instanceof ServiceError) {
      throw error
    }

    throw new ServiceError(
      `API 请求失败: ${error.message || '未知错误'}`,
      undefined,
      'API_ERROR',
      error
    )
  }
}

/**
 * 执行 REST API 请求
 */
async function executeRestApi(
  config: RestApiConfig,
  context: RequestContext
): Promise<ApiResponse> {
  const axiosClient = getDefaultAxiosClient()

  try {
    const response = await axiosClient.request(config)

    return {
      data: response,
      success: true,
    }
  } catch (error) {
    throw new ServiceError(
      `REST API 请求失败: ${error.message || '未知错误'}`,
      error.status,
      'REST_ERROR',
      error
    )
  }
}

/**
 * 执行 OData API 请求
 */
async function executeODataApi(
  config: ODataApiConfig,
  context: RequestContext
): Promise<ApiResponse> {
  // 从 URL 中提取 baseUrl
  const url = new URL(config.url)
  const baseUrl = `${url.protocol}//${url.host}${url.pathname
    .split('/')
    .slice(0, -1)
    .join('/')}`
  const entitySet = url.pathname.split('/').pop() || ''

  const odataClient = createODataClient({
    baseUrl,
    headers: config.headers,
    timeout: config.timeout,
  })

  try {
    // 合并上下文参数到 OData 查询选项
    const queryOptions = {
      ...config.odata,
      ...context.params,
    }

    const response = await odataClient.query(entitySet, queryOptions, context)

    // 如果指定了 dataPath，从响应中提取数据
    if (config.dataPath) {
      const extractedData = extractDataByPath(response.data, config.dataPath)
      return {
        ...response,
        data: extractedData,
      }
    }

    return response
  } catch (error) {
    throw new ServiceError(
      `OData API 请求失败: ${error.message || '未知错误'}`,
      error.status,
      'ODATA_ERROR',
      error
    )
  }
}

/**
 * 从对象中按路径提取数据
 */
function extractDataByPath(data: any, path: string): any {
  const keys = path.split('.')
  let result = data

  for (const key of keys) {
    if (result && typeof result === 'object' && key in result) {
      result = result[key]
    } else {
      throw new ServiceError(`无法在响应中找到路径: ${path}`)
    }
  }

  return result
}

/**
 * 判断是否为 OData 配置
 */
export function isODataConfig(config: ApiConfig): config is ODataApiConfig {
  return config.protocol === 'odata'
}

/**
 * 判断是否为 REST 配置
 */
export function isRestConfig(config: ApiConfig): config is RestApiConfig {
  return !config.protocol || config.protocol === 'rest'
}

/**
 * 创建成功响应
 */
export function createSuccessResponse<T>(
  data: T,
  total?: number
): ApiResponse<T> {
  return {
    data,
    success: true,
    total,
  }
}

/**
 * 创建错误响应
 */
export function createErrorResponse(error: string): ApiResponse {
  return {
    data: null,
    success: false,
    error,
  }
}

/**
 * 格式化 URL
 */
export function formatUrl(baseUrl: string, path: string): string {
  const base = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl
  const pathStr = path.startsWith('/') ? path : `/${path}`
  return `${base}${pathStr}`
}

/**
 * 合并请求头
 */
export function mergeHeaders(
  ...headers: (Record<string, string> | undefined)[]
): Record<string, string> {
  return Object.assign({}, ...headers.filter(Boolean))
}

/**
 * 延迟函数
 */
export function delay(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

/**
 * 重试函数
 */
export async function retry<T>(
  fn: () => Promise<T>,
  maxAttempts: number = 3,
  delayMs: number = 1000
): Promise<T> {
  let lastError: any

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error

      if (attempt === maxAttempts) {
        break
      }

      console.warn(
        `[retry] 第 ${attempt} 次尝试失败，${delayMs}ms 后重试:`,
        error.message
      )
      await delay(delayMs)
    }
  }

  throw new ServiceError(
    `重试 ${maxAttempts} 次后仍然失败: ${lastError.message}`,
    lastError.status,
    'RETRY_FAILED',
    lastError
  )
}

// ============================================================================
// OData Query Builder 工具库
// ============================================================================

/**
 * OData 查询构建器选项
 */
export interface ODataQueryBuilderOptions {
  select?: string | string[]
  filter?: string | Record<string, any>
  orderBy?: string | string[]
  top?: number
  skip?: number
  count?: boolean
  expand?: string | string[]
  search?: string
}

/**
 * OData 过滤器操作符
 */
export const ODataOperators = {
  // 比较操作符
  eq: (field: string, value: any) => `${field} eq ${formatODataValue(value)}`,
  ne: (field: string, value: any) => `${field} ne ${formatODataValue(value)}`,
  gt: (field: string, value: any) => `${field} gt ${formatODataValue(value)}`,
  ge: (field: string, value: any) => `${field} ge ${formatODataValue(value)}`,
  lt: (field: string, value: any) => `${field} lt ${formatODataValue(value)}`,
  le: (field: string, value: any) => `${field} le ${formatODataValue(value)}`,

  // 字符串操作符
  contains: (field: string, value: string) =>
    `contains(${field}, ${formatODataValue(value)})`,
  startswith: (field: string, value: string) =>
    `startswith(${field}, ${formatODataValue(value)})`,
  endswith: (field: string, value: string) =>
    `endswith(${field}, ${formatODataValue(value)})`,
  indexof: (field: string, value: string) =>
    `indexof(${field}, ${formatODataValue(value)})`,
  length: (field: string) => `length(${field})`,
  substring: (field: string, start: number, length?: number) =>
    length !== undefined
      ? `substring(${field}, ${start}, ${length})`
      : `substring(${field}, ${start})`,
  tolower: (field: string) => `tolower(${field})`,
  toupper: (field: string) => `toupper(${field})`,
  trim: (field: string) => `trim(${field})`,

  // 逻辑操作符
  and: (...conditions: string[]) => `(${conditions.join(' and ')})`,
  or: (...conditions: string[]) => `(${conditions.join(' or ')})`,
  not: (condition: string) => `not (${condition})`,

  // 集合操作符
  in: (field: string, values: any[]) =>
    `${field} in (${values.map(formatODataValue).join(',')})`,

  // 日期操作符
  year: (field: string) => `year(${field})`,
  month: (field: string) => `month(${field})`,
  day: (field: string) => `day(${field})`,
  hour: (field: string) => `hour(${field})`,
  minute: (field: string) => `minute(${field})`,
  second: (field: string) => `second(${field})`,
  date: (field: string) => `date(${field})`,
  time: (field: string) => `time(${field})`,

  // 数学操作符
  round: (field: string) => `round(${field})`,
  floor: (field: string) => `floor(${field})`,
  ceiling: (field: string) => `ceiling(${field})`,

  // 空值检查
  isNull: (field: string) => `${field} eq null`,
  isNotNull: (field: string) => `${field} ne null`,
} as const

/**
 * 格式化 OData 值
 */
function formatODataValue(value: any): string {
  if (value === null || value === undefined) {
    return 'null'
  }

  if (typeof value === 'string') {
    return `'${value.replace(/'/g, "''")}'`
  }

  if (typeof value === 'boolean') {
    return value.toString()
  }

  if (typeof value === 'number') {
    return value.toString()
  }

  if (value instanceof Date) {
    return value.toISOString()
  }

  // 对于其他类型，尝试转换为字符串
  return `'${String(value).replace(/'/g, "''")}'`
}

/**
 * 构建 OData 查询字符串
 */
export function buildODataQuery(options: ODataQueryBuilderOptions): string {
  const params: string[] = []

  // $select
  if (options.select) {
    const selectFields = Array.isArray(options.select)
      ? options.select.join(',')
      : options.select
    params.push(`$select=${encodeURIComponent(selectFields)}`)
  }

  // $filter
  if (options.filter) {
    const filterStr =
      typeof options.filter === 'string'
        ? options.filter
        : buildFilterFromObject(options.filter)
    params.push(`$filter=${encodeURIComponent(filterStr)}`)
  }

  // $orderby
  if (options.orderBy) {
    const orderByStr = Array.isArray(options.orderBy)
      ? options.orderBy.join(',')
      : options.orderBy
    params.push(`$orderby=${encodeURIComponent(orderByStr)}`)
  }

  // $top
  if (options.top !== undefined) {
    params.push(`$top=${options.top}`)
  }

  // $skip
  if (options.skip !== undefined) {
    params.push(`$skip=${options.skip}`)
  }

  // $count
  if (options.count) {
    params.push('$count=true')
  }

  // $expand
  if (options.expand) {
    const expandStr = Array.isArray(options.expand)
      ? options.expand.join(',')
      : options.expand
    params.push(`$expand=${encodeURIComponent(expandStr)}`)
  }

  // $search
  if (options.search) {
    params.push(`$search=${encodeURIComponent(options.search)}`)
  }

  return params.join('&')
}

/**
 * 从对象构建过滤器字符串
 */
function buildFilterFromObject(filterObj: Record<string, any>): string {
  const conditions: string[] = []

  for (const [key, value] of Object.entries(filterObj)) {
    if (key === 'and' && Array.isArray(value)) {
      const andConditions = value.map((v) =>
        typeof v === 'string' ? v : buildFilterFromObject(v)
      )
      conditions.push(ODataOperators.and(...andConditions))
    } else if (key === 'or' && Array.isArray(value)) {
      const orConditions = value.map((v) =>
        typeof v === 'string' ? v : buildFilterFromObject(v)
      )
      conditions.push(ODataOperators.or(...orConditions))
    } else if (key === 'not') {
      const notCondition =
        typeof value === 'string' ? value : buildFilterFromObject(value)
      conditions.push(ODataOperators.not(notCondition))
    } else if (typeof value === 'object' && value !== null) {
      // 处理字段级操作符
      for (const [operator, operatorValue] of Object.entries(value)) {
        switch (operator) {
          case 'eq':
            conditions.push(ODataOperators.eq(key, operatorValue))
            break
          case 'ne':
            conditions.push(ODataOperators.ne(key, operatorValue))
            break
          case 'gt':
            conditions.push(ODataOperators.gt(key, operatorValue))
            break
          case 'ge':
            conditions.push(ODataOperators.ge(key, operatorValue))
            break
          case 'lt':
            conditions.push(ODataOperators.lt(key, operatorValue))
            break
          case 'le':
            conditions.push(ODataOperators.le(key, operatorValue))
            break
          case 'contains':
            conditions.push(ODataOperators.contains(key, operatorValue))
            break
          case 'startswith':
            conditions.push(ODataOperators.startswith(key, operatorValue))
            break
          case 'endswith':
            conditions.push(ODataOperators.endswith(key, operatorValue))
            break
          case 'in':
            conditions.push(ODataOperators.in(key, operatorValue))
            break
          case 'isNull':
            conditions.push(ODataOperators.isNull(key))
            break
          case 'isNotNull':
            conditions.push(ODataOperators.isNotNull(key))
            break
          default:
            // 默认使用 eq 操作符
            conditions.push(ODataOperators.eq(key, operatorValue))
        }
      }
    } else {
      // 简单的等值比较
      conditions.push(ODataOperators.eq(key, value))
    }
  }

  return conditions.join(' and ')
}

/**
 * OData 查询构建器类
 */
export class ODataQueryBuilder {
  private options: ODataQueryBuilderOptions = {}

  /**
   * 选择字段
   */
  select(fields: string | string[]): this {
    this.options.select = fields
    return this
  }

  /**
   * 添加过滤条件
   */
  filter(condition: string | Record<string, any>): this {
    this.options.filter = condition
    return this
  }

  /**
   * 排序
   */
  orderBy(fields: string | string[]): this {
    this.options.orderBy = fields
    return this
  }

  /**
   * 限制返回数量
   */
  top(count: number): this {
    this.options.top = count
    return this
  }

  /**
   * 跳过数量
   */
  skip(count: number): this {
    this.options.skip = count
    return this
  }

  /**
   * 包含总数
   */
  count(include: boolean = true): this {
    this.options.count = include
    return this
  }

  /**
   * 展开关联实体
   */
  expand(fields: string | string[]): this {
    this.options.expand = fields
    return this
  }

  /**
   * 搜索
   */
  search(term: string): this {
    this.options.search = term
    return this
  }

  /**
   * 构建查询字符串
   */
  build(): string {
    return buildODataQuery(this.options)
  }

  /**
   * 重置构建器
   */
  reset(): this {
    this.options = {}
    return this
  }
}

/**
 * 创建 OData 查询构建器
 */
export function createODataQueryBuilder(): ODataQueryBuilder {
  return new ODataQueryBuilder()
}

/**
 * 便捷的过滤器构建函数
 */
export const ODataFilters = {
  /**
   * 等于
   */
  eq: (field: string, value: any) => ({ [field]: { eq: value } }),

  /**
   * 不等于
   */
  ne: (field: string, value: any) => ({ [field]: { ne: value } }),

  /**
   * 大于
   */
  gt: (field: string, value: any) => ({ [field]: { gt: value } }),

  /**
   * 大于等于
   */
  ge: (field: string, value: any) => ({ [field]: { ge: value } }),

  /**
   * 小于
   */
  lt: (field: string, value: any) => ({ [field]: { lt: value } }),

  /**
   * 小于等于
   */
  le: (field: string, value: any) => ({ [field]: { le: value } }),

  /**
   * 包含
   */
  contains: (field: string, value: string) => ({
    [field]: { contains: value },
  }),

  /**
   * 开始于
   */
  startswith: (field: string, value: string) => ({
    [field]: { startswith: value },
  }),

  /**
   * 结束于
   */
  endswith: (field: string, value: string) => ({
    [field]: { endswith: value },
  }),

  /**
   * 在集合中
   */
  in: (field: string, values: any[]) => ({ [field]: { in: values } }),

  /**
   * 为空
   */
  isNull: (field: string) => ({ [field]: { isNull: true } }),

  /**
   * 不为空
   */
  isNotNull: (field: string) => ({ [field]: { isNotNull: true } }),

  /**
   * AND 条件
   */
  and: (...conditions: Record<string, any>[]) => ({ and: conditions }),

  /**
   * OR 条件
   */
  or: (...conditions: Record<string, any>[]) => ({ or: conditions }),

  /**
   * NOT 条件
   */
  not: (condition: Record<string, any>) => ({ not: condition }),
} as const
