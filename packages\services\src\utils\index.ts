/**
 * 工具函数
 */

import {
  ApiConfig,
  ApiResponse,
  RequestContext,
  RestApiConfig,
  ODataApiConfig,
  ServiceError,
} from '../types'
import { getDefaultAxiosClient } from '../clients/axios-client'
import { createODataClient } from '../clients/odata-client'

/**
 * 统一的 API 执行函数
 */
export async function executeApi(
  config: ApiConfig,
  context: RequestContext = {}
): Promise<ApiResponse> {
  try {
    console.log('[executeApi] 执行 API 请求:', config)

    // 根据协议类型选择对应的客户端
    if (config.protocol === 'odata') {
      return await executeODataApi(config as ODataApiConfig, context)
    } else {
      return await executeRestApi(config as RestApiConfig, context)
    }
  } catch (error) {
    console.error('[executeApi] API 请求失败:', error)
    
    if (error instanceof ServiceError) {
      throw error
    }
    
    throw new ServiceError(
      `API 请求失败: ${error.message || '未知错误'}`,
      undefined,
      'API_ERROR',
      error
    )
  }
}

/**
 * 执行 REST API 请求
 */
async function executeRestApi(
  config: RestApiConfig,
  context: RequestContext
): Promise<ApiResponse> {
  const axiosClient = getDefaultAxiosClient()
  
  try {
    const response = await axiosClient.request(config)
    
    return {
      data: response,
      success: true,
    }
  } catch (error) {
    throw new ServiceError(
      `REST API 请求失败: ${error.message || '未知错误'}`,
      error.status,
      'REST_ERROR',
      error
    )
  }
}

/**
 * 执行 OData API 请求
 */
async function executeODataApi(
  config: ODataApiConfig,
  context: RequestContext
): Promise<ApiResponse> {
  // 从 URL 中提取 baseUrl
  const url = new URL(config.url)
  const baseUrl = `${url.protocol}//${url.host}${url.pathname.split('/').slice(0, -1).join('/')}`
  const entitySet = url.pathname.split('/').pop() || ''
  
  const odataClient = createODataClient({
    baseUrl,
    headers: config.headers,
    timeout: config.timeout,
  })
  
  try {
    // 合并上下文参数到 OData 查询选项
    const queryOptions = {
      ...config.odata,
      ...context.params,
    }
    
    const response = await odataClient.query(entitySet, queryOptions, context)
    
    // 如果指定了 dataPath，从响应中提取数据
    if (config.dataPath) {
      const extractedData = extractDataByPath(response.data, config.dataPath)
      return {
        ...response,
        data: extractedData,
      }
    }
    
    return response
  } catch (error) {
    throw new ServiceError(
      `OData API 请求失败: ${error.message || '未知错误'}`,
      error.status,
      'ODATA_ERROR',
      error
    )
  }
}

/**
 * 从对象中按路径提取数据
 */
function extractDataByPath(data: any, path: string): any {
  const keys = path.split('.')
  let result = data
  
  for (const key of keys) {
    if (result && typeof result === 'object' && key in result) {
      result = result[key]
    } else {
      throw new ServiceError(`无法在响应中找到路径: ${path}`)
    }
  }
  
  return result
}

/**
 * 判断是否为 OData 配置
 */
export function isODataConfig(config: ApiConfig): config is ODataApiConfig {
  return config.protocol === 'odata'
}

/**
 * 判断是否为 REST 配置
 */
export function isRestConfig(config: ApiConfig): config is RestApiConfig {
  return !config.protocol || config.protocol === 'rest'
}

/**
 * 创建成功响应
 */
export function createSuccessResponse<T>(data: T, total?: number): ApiResponse<T> {
  return {
    data,
    success: true,
    total,
  }
}

/**
 * 创建错误响应
 */
export function createErrorResponse(error: string): ApiResponse {
  return {
    data: null,
    success: false,
    error,
  }
}

/**
 * 格式化 URL
 */
export function formatUrl(baseUrl: string, path: string): string {
  const base = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl
  const pathStr = path.startsWith('/') ? path : `/${path}`
  return `${base}${pathStr}`
}

/**
 * 合并请求头
 */
export function mergeHeaders(
  ...headers: (Record<string, string> | undefined)[]
): Record<string, string> {
  return Object.assign({}, ...headers.filter(Boolean))
}

/**
 * 延迟函数
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 重试函数
 */
export async function retry<T>(
  fn: () => Promise<T>,
  maxAttempts: number = 3,
  delayMs: number = 1000
): Promise<T> {
  let lastError: any
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error
      
      if (attempt === maxAttempts) {
        break
      }
      
      console.warn(`[retry] 第 ${attempt} 次尝试失败，${delayMs}ms 后重试:`, error.message)
      await delay(delayMs)
    }
  }
  
  throw new ServiceError(
    `重试 ${maxAttempts} 次后仍然失败: ${lastError.message}`,
    lastError.status,
    'RETRY_FAILED',
    lastError
  )
}
