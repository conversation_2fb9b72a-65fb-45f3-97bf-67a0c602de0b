export function extractIds<T extends { id: string | number; children?: T[] }>(
  data: T[],
  childrenKey = 'children'
): (string | number)[] {
  const ids: (string | number)[] = []

  function traverse(nodes: T[]) {
    for (const node of nodes) {
      ids.push(node.id)
      const children = node[childrenKey as keyof T] as T[] | undefined
      if (Array.isArray(children)) {
        traverse(children)
      }
    }
  }

  traverse(data)
  return ids
}
