declare module 'vue' {
  // GlobalComponents for Volar
  export interface GlobalComponents {
    NeButton: typeof import('neue-plus')['NeButton']
    NeInput: typeof import('neue-plus')['NeInput']
    NeDateRange: typeof import('neue-plus')['NeDateRange']
    NeCheckboxGroup: typeof import('neue-plus')['NeCheckboxGroup']
    NeSelect: typeof import('neue-plus')['NeSelect']
    NeRadioGroup: typeof import('neue-plus')['NeRadioGroup']
    NeCard: typeof import('neue-plus')['NeCard']
    NeTable: typeof import('neue-plus')['NeTable']
    NeProTable: typeof import('neue-plus')['NeProTable']
    NeProForm: typeof import('neue-plus')['NeProForm']
    NeRadioGroup: typeof import('neue-plus')['NeRadioGroup']
    NeConfigProvider: typeof import('neue-plus')['NeConfigProvider']
    NeTooltipEllipsis: typeof import('neue-plus')['NeTooltipEllipsis']
    NeAvatar: typeof import('neue-plus')['NeAvatar']
    NeWidgetWrapper: typeof import('neue-plus')['NeWidgetWrapper']
    NeWidgetContent: typeof import('neue-plus')['NeWidgetContent']
    NeWidget: typeof import('neue-plus')['NeWidget']
    NePagination: typeof import('neue-plus')['NePagination']
  }

  interface ComponentCustomProperties {
    $message: typeof import('neue-plus')['ElMessage']
  }
}

export {}
