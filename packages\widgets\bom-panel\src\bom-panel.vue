<template>
  <widget-wrapper>
    <el-drawer :model-value="visible" @close="handleClose">
      <div>{{ JSON.stringify(params) }}</div>
      <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
        <el-tab-pane label="User" name="first">User</el-tab-pane>
        <el-tab-pane label="Config" name="second">Config</el-tab-pane>
        <el-tab-pane label="Role" name="third">Role</el-tab-pane>
        <el-tab-pane label="Task" name="fourth">Task</el-tab-pane>
      </el-tabs>
    </el-drawer>
  </widget-wrapper>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import WidgetWrapper from '@neue-plus/components/widget-wrapper'

defineOptions({
  name: 'BomPanel',
  inheritAttrs: false,
})
const activeName = ref('first')
const visible = ref(false)

const handleClick = (tab: any) => {
  console.log(tab)
}
defineProps({
  items: {
    type: Array<{
      label: string
      name: string
    }>,
    default: () => [],
  },
})
const emit = defineEmits(['rowClick'])
function rowClick(row: any) {
  emit('rowClick', row)
}
const handleClose = () => {
  visible.value = false
}
const params = ref({})
defineExpose({
  rowClick,
  describe: 'describe',
  open: (val: any, p: any) => {
    params.value = p
    visible.value = true
  },
})
</script>
