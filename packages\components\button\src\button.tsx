import { defineComponent, shallowRef, watchEffect } from 'vue'
import { ElButton, buttonProps } from 'element-plus'
import * as icons from '@element-plus/icons-vue'
import type { Component } from 'vue'

const NeButton = defineComponent({
  name: '<PERSON>e<PERSON>utt<PERSON>',
  props: buttonProps,
  setup(props, { slots }) {
    const { icon } = props
    const iconComponent = shallowRef<Component | null>(null)
    watchEffect(() => {
      if (icon && (icon as string) in icons) {
        iconComponent.value = icons[icon as keyof typeof icons] as Component
      } else {
        iconComponent.value = null
      }
    })
    return () => (
      <ElButton {...props} icon={iconComponent.value || undefined}>
        {{
          ...slots,
        }}
      </ElButton>
    )
  },
})

export default NeButton
