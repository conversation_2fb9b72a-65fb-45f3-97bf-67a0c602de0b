# 🔄 统一响应数据处理器使用指南

## 概述

`ResponseProcessor` 是一个统一的响应数据处理器，用于处理 GET、POST、PUT、PATCH、DELETE 五种 HTTP 方法的响应数据，能够自动判断返回数据是单条记录还是集合，并提供统一的响应格式。

## 🎯 核心功能

### 1. 自动数据类型识别

- **单条记录** (SINGLE): 对象类型的数据
- **集合数据** (COLLECTION): 数组类型的数据
- **空数据** (EMPTY): null、undefined 或空数组
- **未知类型** (UNKNOWN): 需要进一步分析的数据

### 2. 智能推断机制

- **根据 HTTP 方法推断**: POST/PUT/PATCH 通常返回单条，GET 可能是单条或集合
- **根据 URL 推断**: 包含 ID 的 URL 通常返回单条记录
- **根据数据结构推断**: 自动识别 OData 格式、数组格式等

### 3. 统一响应格式

```typescript
interface ApiResponse<T = any> {
  data: T | T[] // 实际数据
  success: boolean // 请求是否成功
  total?: number // 总记录数
  page?: number // 当前页码
  pageSize?: number // 每页大小
  hasNext?: boolean // 是否有下一页
  hasPrev?: boolean // 是否有上一页
  error?: string // 错误信息
}
```

## 🔧 使用方法

### 1. 基础使用

```typescript
import { ResponseProcessor } from '../core/response-processor'

// 处理任意响应数据
const response = ResponseProcessor.processResponse(rawData, config)
```

### 2. 按 HTTP 方法处理

```typescript
// GET 请求
const getResponse = ResponseProcessor.processGetResponse(rawData, config)

// POST 请求
const postResponse = ResponseProcessor.processPostResponse(rawData, config)

// PUT/PATCH 请求
const updateResponse = ResponseProcessor.processUpdateResponse(rawData, config)

// DELETE 请求
const deleteResponse = ResponseProcessor.processDeleteResponse(rawData, config)
```

### 3. 自定义配置

```typescript
const response = ResponseProcessor.processResponse(rawData, config, {
  forceDataType: DataType.COLLECTION, // 强制指定数据类型
  collectionPath: 'data.items', // 自定义集合数据路径
  preserveOriginal: true, // 保留原始响应结构
  customExtractor: (data) => data.result, // 自定义数据提取逻辑
})
```

## 📊 数据类型识别示例

### 1. 单条记录识别

```typescript
// 输入数据
const singleRecord = {
  id: 1,
  name: 'John Doe',
  email: '<EMAIL>'
}

// 处理结果
{
  data: { id: 1, name: 'John Doe', email: '<EMAIL>' },
  success: true,
  total: 1,
  page: 1,
  pageSize: 1
}
```

### 2. 集合数据识别

```typescript
// 输入数据
const collection = [
  { id: 1, name: 'John' },
  { id: 2, name: 'Jane' }
]

// 处理结果
{
  data: [{ id: 1, name: 'John' }, { id: 2, name: 'Jane' }],
  success: true,
  total: 2,
  pageSize: 2
}
```

### 3. OData 格式识别

```typescript
// 输入数据
const odataResponse = {
  '@odata.context': '...',
  '@odata.count': 100,
  value: [
    { id: 1, name: 'John' },
    { id: 2, name: 'Jane' }
  ]
}

// 处理结果
{
  data: [{ id: 1, name: 'John' }, { id: 2, name: 'Jane' }],
  success: true,
  total: 100,
  pageSize: 2,
  hasNext: false  // 根据 @odata.nextLink 判断
}
```

## 🎯 HTTP 方法特定处理

### 1. GET 请求处理

```typescript
// 自动根据 URL 判断数据类型
// /api/users -> 集合数据
// /api/users/123 -> 单条记录

const config = {
  url: '/api/users/123',
  method: 'get',
  protocol: 'rest',
}

const response = ResponseProcessor.processGetResponse(userData, config)
// 自动识别为单条记录
```

### 2. POST 请求处理

```typescript
// POST 通常返回创建的单条记录
const config = {
  url: '/api/users',
  method: 'post',
  protocol: 'rest',
}

const response = ResponseProcessor.processPostResponse(createdUser, config)
// 强制处理为单条记录
```

### 3. PUT/PATCH 请求处理

```typescript
// PUT/PATCH 通常返回更新后的单条记录
const config = {
  url: '/api/users/123',
  method: 'put',
  protocol: 'rest',
}

const response = ResponseProcessor.processUpdateResponse(updatedUser, config)
// 强制处理为单条记录
```

### 4. DELETE 请求处理

```typescript
// DELETE 通常返回空数据或确认信息
const config = {
  url: '/api/users/123',
  method: 'delete',
  protocol: 'rest',
}

const response = ResponseProcessor.processDeleteResponse(deleteResult, config)
// 强制处理为空数据类型
```

## 🔍 URL 模式识别

### 单条记录 URL 模式

```typescript
'/api/users/123' // 数字 ID
'/api/users/john-doe' // 字符串 ID
'/api/People(\'key\')' // OData key 格式
'/api/users/550e8400-e29b-41d4-a716-************' // UUID
```

### 集合数据 URL 模式

```typescript
'/api/users' // 基础集合
'/api/users?page=1' // 带查询参数的集合
'/api/departments/1/users' // 嵌套集合
```

## 🛠️ 在适配器中的集成

### REST 适配器集成

```typescript
// packages/services/src/adapters/rest-adapter.ts
import { ResponseProcessor } from '../core/response-processor'

export class RestAdapter {
  async execute(
    config: ApiConfig,
    context: RequestContext
  ): Promise<ApiResponse> {
    try {
      const response = await this.httpClient.request(requestConfig)

      // 使用统一的响应处理器
      return ResponseProcessor.processResponse(response, config, {
        collectionPath: config.dataPath,
      })
    } catch (error) {
      // 错误处理
    }
  }
}
```

### OData 适配器集成

```typescript
// packages/services/src/adapters/odata-adapter.ts
import { ResponseProcessor } from '../core/response-processor'

export class ODataAdapter {
  private async handleGet(
    client,
    config,
    entitySet,
    context
  ): Promise<ApiResponse> {
    const rawData = await client.query(entitySet, config.query, context)

    // 使用统一的响应处理器
    return ResponseProcessor.processGetResponse(rawData, config, {
      collectionPath: config.dataPath,
    })
  }

  private async handlePost(
    client,
    config,
    entitySet,
    context
  ): Promise<ApiResponse> {
    const rawData = await client.create(parsedBody, entitySet)

    // 使用统一的响应处理器
    return ResponseProcessor.processPostResponse(rawData, config)
  }
}
```

## 🧪 测试用例

### 1. 数据类型分析测试

```typescript
import { ResponseProcessor, DataType } from '../core/response-processor'

// 测试单条记录
const singleAnalysis = ResponseProcessor.analyzeResponse({
  id: 1,
  name: 'John',
})
expect(singleAnalysis.dataType).toBe(DataType.SINGLE)
expect(singleAnalysis.itemCount).toBe(1)

// 测试集合数据
const collectionAnalysis = ResponseProcessor.analyzeResponse([
  { id: 1 },
  { id: 2 },
])
expect(collectionAnalysis.dataType).toBe(DataType.COLLECTION)
expect(collectionAnalysis.itemCount).toBe(2)

// 测试 OData 格式
const odataAnalysis = ResponseProcessor.analyzeResponse({
  value: [{ id: 1 }],
  '@odata.count': 10,
})
expect(odataAnalysis.dataType).toBe(DataType.COLLECTION)
expect(odataAnalysis.hasMetadata).toBe(true)
expect(odataAnalysis.hasPagination).toBe(true)
```

### 2. HTTP 方法推断测试

```typescript
// 测试方法推断
expect(ResponseProcessor.inferDataTypeByMethod('get')).toBe(DataType.UNKNOWN)
expect(ResponseProcessor.inferDataTypeByMethod('post')).toBe(DataType.SINGLE)
expect(ResponseProcessor.inferDataTypeByMethod('delete')).toBe(DataType.EMPTY)

// 测试 URL 推断
expect(ResponseProcessor.inferDataTypeByUrl('/api/users/123')).toBe(
  DataType.SINGLE
)
expect(ResponseProcessor.inferDataTypeByUrl('/api/users')).toBe(
  DataType.COLLECTION
)
```

## 🎉 优势和特性

### 1. 统一性

- 所有 HTTP 方法使用相同的响应格式
- 统一的数据类型识别逻辑
- 一致的错误处理机制

### 2. 智能性

- 自动识别数据类型
- 智能推断预期格式
- 支持多种数据结构

### 3. 灵活性

- 支持自定义配置
- 可扩展的数据提取逻辑
- 兼容不同的 API 协议

### 4. 可维护性

- 集中的响应处理逻辑
- 清晰的代码结构
- 完善的类型定义

## 🔮 未来扩展

### 1. 更多数据格式支持

- GraphQL 响应格式
- XML 数据格式
- 自定义协议格式

### 2. 高级功能

- 响应数据缓存
- 数据转换管道
- 性能监控集成

### 3. 开发工具

- 响应数据调试器
- 类型推断可视化
- 性能分析工具
