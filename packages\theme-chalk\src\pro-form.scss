@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(pro-form) {
  .el-form-item {
    margin-bottom: 18px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  // 内联表单样式
  &.el-form--inline {
    .el-form-item {
      margin-right: 16px;
      margin-bottom: 0;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  // 按钮组样式
  .el-form-item__content {
    .el-button + .el-button {
      margin-left: 8px;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .el-col {
      margin-bottom: 16px;
    }

    &.el-form--inline {
      .el-form-item {
        display: block;
        margin-right: 0;
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
