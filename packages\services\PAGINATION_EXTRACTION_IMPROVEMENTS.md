# 🔧 分页信息提取改进 - 使用 lodash get 方法

## 问题发现

你发现了一个重要问题：在 `extractPagination` 方法中，我们还在使用传统的方括号访问方式：

```typescript
// 修改前：不安全的访问方式
if ('@odata.count' in rawData) {
  pagination.total = rawData['@odata.count'] // ❌ 没有使用 lodash get
}
```

## 🚀 改进方案

### 1. **使用 lodash get 方法提取分页信息**

#### **修改前**：

```typescript
static extractPagination(rawData: any, analysis: ResponseAnalysis) {
  const pagination: any = {}

  if (analysis.hasMetadata && rawData && typeof rawData === 'object') {
    // ❌ 传统的不安全访问方式
    if ('@odata.count' in rawData) {
      pagination.total = rawData['@odata.count']
    }
    if ('@odata.nextLink' in rawData) {
      pagination.hasNext = true
    }
    if ('@odata.prevLink' in rawData) {
      pagination.hasPrev = true
    }
  }

  return pagination
}
```

#### **修改后**：

```typescript
static extractPagination(rawData: any, analysis: ResponseAnalysis) {
  const pagination: any = {}

  if (analysis.hasMetadata && rawData && typeof rawData === 'object') {
    // ✅ 使用 lodash get 方法安全提取，支持多种格式
    const total = this.extractDataByPaths(rawData, [
      '@odata.count',        // OData v4 标准格式
      'd.__count',           // OData v2 格式
      'count',               // 自定义格式
      'total',               // 通用格式
      'totalCount'           // 备选格式
    ])

    if (total !== undefined && total !== null) {
      pagination.total = Number(total)
    }

    // 其他分页信息的安全提取...
  }

  return pagination
}
```

### 2. **支持多种分页格式**

#### **总记录数提取**

```typescript
const total = this.extractDataByPaths(rawData, [
  '@odata.count', // OData v4: { "@odata.count": 100, "value": [...] }
  'd.__count', // OData v2: { "d": { "__count": 100, "results": [...] } }
  'count', // 自定义: { "count": 100, "data": [...] }
  'total', // 通用: { "total": 100, "items": [...] }
  'totalCount', // 备选: { "totalCount": 100, "records": [...] }
])
```

#### **下一页检查**

```typescript
const hasNext = this.extractDataByPaths(rawData, [
  '@odata.nextLink', // OData v4: { "@odata.nextLink": "...?$skip=20" }
  'd.__next', // OData v2: { "d": { "__next": "..." } }
  'nextLink', // 自定义: { "nextLink": "..." }
  'hasNext', // 布尔值: { "hasNext": true }
])
```

#### **页面大小提取**

```typescript
const pageSize = this.extractDataByPaths(rawData, [
  '@odata.top', // OData 查询参数
  'pageSize', // 自定义格式
  'limit', // 通用格式
  'size', // 备选格式
])
```

#### **当前页计算**

```typescript
const currentPage = this.extractDataByPaths(rawData, [
  '@odata.skip', // OData skip 参数（需要计算页码）
  'page', // 直接页码
  'currentPage', // 自定义格式
  'pageNumber', // 备选格式
])

// 智能页码计算
if (currentPage !== undefined && currentPage !== null) {
  if (typeof currentPage === 'number' && currentPage > 0) {
    pagination.page = currentPage
  } else if (typeof currentPage === 'number' && pagination.pageSize) {
    // 如果是 skip 值，计算页码
    pagination.page = Math.floor(currentPage / pagination.pageSize) + 1
  }
}
```

### 3. **支持的响应格式示例**

#### **OData v4 格式**

```typescript
{
  "@odata.context": "...",
  "@odata.count": 100,
  "@odata.nextLink": "...?$skip=20",
  "value": [...]
}

// 提取结果：
// total: 100
// hasNext: true
// pageSize: 20 (从 nextLink 计算)
// page: 1
```

#### **OData v2 格式**

```typescript
{
  "d": {
    "__count": 100,
    "__next": "...?$skip=20",
    "results": [...]
  }
}

// 提取结果：
// total: 100
// hasNext: true
// pageSize: 20
// page: 1
```

#### **自定义 REST API 格式**

```typescript
{
  "data": [...],
  "pagination": {
    "total": 100,
    "page": 1,
    "pageSize": 20,
    "hasNext": true,
    "hasPrev": false
  }
}

// 需要配置 collectionPath: 'data'
// 分页信息会从 pagination 对象中提取
```

#### **简单格式**

```typescript
{
  "items": [...],
  "count": 100,
  "hasNext": true
}

// 提取结果：
// total: 100
// hasNext: true
// pageSize: items.length
// page: 1
```

## 🎯 改进效果

### 1. **更安全的数据访问**

```typescript
// 修改前：可能抛出错误
const total = rawData['@odata.count'] // 如果属性不存在可能有问题

// 修改后：安全访问
const total = this.extractDataByPaths(rawData, ['@odata.count', 'total'], 0)
```

### 2. **多格式兼容性**

```typescript
// 支持多种 API 响应格式
const total = this.extractDataByPaths(rawData, [
  '@odata.count', // OData v4
  'd.__count', // OData v2
  'total', // REST API
  'count', // 自定义
  'totalCount', // 备选
])
```

### 3. **智能类型转换**

```typescript
// 自动处理字符串到数字的转换
if (total !== undefined && total !== null) {
  pagination.total = Number(total) // "100" -> 100
}
```

### 4. **更好的默认值处理**

```typescript
// 如果是数组，设置合理的默认值
if (analysis.isArray) {
  if (!pagination.pageSize) {
    pagination.pageSize = analysis.itemCount
  }
  if (!pagination.total) {
    pagination.total = analysis.itemCount
  }
  if (!pagination.page) {
    pagination.page = 1
  }
}
```

## 📊 实际应用示例

### 1. **OData 服务**

```typescript
// TripPin 服务响应
{
  "@odata.context": "https://services.odata.org/V4/TripPinServiceRW/$metadata#People",
  "@odata.count": 20,
  "@odata.nextLink": "https://services.odata.org/V4/TripPinServiceRW/People?$skip=5&$top=5",
  "value": [
    { "UserName": "russellwhyte", "FirstName": "Russell" },
    // ... 更多数据
  ]
}

// 提取结果：
{
  total: 20,
  page: 1,
  pageSize: 5,
  hasNext: true,
  hasPrev: false
}
```

### 2. **自定义 REST API**

```typescript
// 自定义 API 响应
{
  "success": true,
  "data": {
    "users": [...],
    "meta": {
      "totalCount": 150,
      "currentPage": 2,
      "pageSize": 10,
      "hasNextPage": true
    }
  }
}

// 配置：collectionPath: 'data.users'
// 提取结果：
{
  total: 150,
  page: 2,
  pageSize: 10,
  hasNext: true,
  hasPrev: true
}
```

## ✅ 总结

通过使用 lodash 的 `get` 方法改进分页信息提取：

1. **✅ 更安全**：避免了直接属性访问可能的错误
2. **✅ 更兼容**：支持多种 OData 版本和自定义格式
3. **✅ 更智能**：自动类型转换和页码计算
4. **✅ 更健壮**：提供合理的默认值和错误处理
5. **✅ 更灵活**：支持多种路径尝试机制

现在分页信息提取功能能够处理各种复杂的 API 响应格式，同时保持代码的安全性和可维护性！
