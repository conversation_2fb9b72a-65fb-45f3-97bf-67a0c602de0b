/**
 * @neue-plus/services
 * 统一的服务层框架，支持 REST 和 OData 协议
 */

// 核心类型导出
export type {
  // 基础类型
  Protocol,
  HttpMethod,
  RequestContext,
  ApiResponse,
  ErrorHandler,
  ExpressionParser,

  // API 配置类型
  ApiConfig,
  RestApiConfig,
  ODataApiConfig,
  LegacyApiSchema,

  // OData 相关类型
  ODataQueryOptions,
  ODataEntity,
  ODataServiceResponse,
  ODataClientConfig,
  ODataFilterRule,
  FilterOperator,
  AuthConfig,

  // 接口类型
  ApiAdapter,
  ApiClient,
  IODataClient,
} from './core/types'

// 新架构接口导出
export type {
  // 核心接口
  RequestExecutor,
  ConfigNormalizer,
  ProtocolAdapter,
  ClientFactory as IClientFactory,
  Middleware,
  Plugin,
  PluginContext,
  ServiceContainer,
} from './core/interfaces'

// 核心功能导出
export {
  // 表达式解析
  parseExpression,
  setExpressionParser,
  getExpressionParser,
  createCustomParser,
  DefaultExpressionParser,
} from './core/expression-parser'

export {
  // 错误处理
  setErrorHandler,
  getErrorHandler,
  handleError,
  createServiceError,
  createErrorFromResponse,
  withRetry,

  // 错误类
  ServiceError,
  NetworkError,
  AuthenticationError,
  AuthorizationError,
  ValidationError,
  TimeoutError,
} from './core/error-handler'

// 新架构核心组件导出
export {
  // 服务容器
  DefaultServiceContainer,
  getGlobalContainer,
  setGlobalContainer,
  createContainer,
  Service,
  Inject,
  registerService,
  getService,
  hasService,
} from './core/container'

export {
  // 中间件管理
  MiddlewareManager,
  createLoggingMiddleware,
  createPerformanceMiddleware,
  createRetryMiddleware,
} from './core/middleware'

export {
  // 插件系统
  PluginManager,
  getGlobalPluginManager,
  setGlobalPluginManager,
  createPluginManager,
  installPlugin,
  uninstallPlugin,
  createPlugin,
  createAdapterPlugin,
  createMiddlewarePlugin,
  createNormalizerPlugin,
  installPlugins,
  installPluginsFromConfig,
} from './core/plugin-system'

export {
  // 配置标准化器
  LegacyConfigNormalizer,
  RestConfigNormalizer,
  ODataConfigNormalizer,
  CompositeConfigNormalizer,
  createDefaultNormalizer,
  getGlobalNormalizer,
  setGlobalNormalizer,
  normalizeConfig,
  canNormalizeConfig,
} from './core/normalizers'

export {
  // 初始化和引导
  initialize,
  quickStart,
  initializeForDevelopment,
  initializeForProduction,
  reset,
  getFrameworkStatus,
  isInitialized,
  autoInitialize,
  createInitializeConfig,
  validateInitializeConfig,
} from './core/bootstrap'

export type { InitializeOptions } from './core/bootstrap'

// 客户端导出
export {
  HttpClient,
  getDefaultHttpClient,
  setDefaultHttpClient,
  createHttpClient,
} from './clients/http-client'

export { ODataClient } from './clients/odata-client'

// 适配器导出
export {
  RestAdapter,
  getDefaultRestAdapter,
  setDefaultRestAdapter,
  createRestAdapter,
} from './adapters/rest-adapter'

export {
  ODataAdapter,
  getDefaultODataAdapter,
  setDefaultODataAdapter,
  createODataAdapter,
} from './adapters/odata-adapter'

// 服务导出
export {
  ApiService,
  getDefaultApiService,
  setDefaultApiService,
  createApiService,
  executeApi,
  executeBatchApi,
  executeConcurrentApi,
} from './services/api-service'

// 工厂导出
export {
  createODataClient,
  getOrCreateODataClient,
  getOrCreateHttpClient,
  clearODataClientCache,
  clearHttpClientCache,
  clearAllClientCache,
  ClientFactory,
  getDefaultClientFactory,
  setDefaultClientFactory,
  createClientFactory,
} from './factories/client-factory'

// 响应处理器导出
export { ResponseProcessor, DataType } from './core/response-processor'
export type {
  ResponseAnalysis,
  ProcessorOptions,
} from './core/response-processor'

// OData 工具导出
export { ODataFilterBuilder, filter } from './utils/odata-filter-builder'
export {
  ODataQueryBuilder,
  createODataQuery,
  query,
} from './utils/odata-query-builder'

// 向后兼容的导出（保持旧的 API 可用）
export { executeApi as execute } from './services/api-service'
export { parseExpression as parse } from './core/expression-parser'

// 默认导出（主要的 API 函数）
export { executeApi as default } from './services/api-service'
