import { computed, defineComponent, ref } from 'vue'
import NeTable from '../../table'
import { neTreeTableProps } from './types'

const NeTreeTable = defineComponent({
  name: 'NeTreeTable',
  props: neTreeTableProps,
  setup(props, { slots, expose }) {
    const tableRef = ref()
    expose({
      toggleExpandAll: () => {
        tableRef.value?.toggleExpandAll()
      },
      refresh: () => {
        tableRef.value?.refresh?.()
      },
    })
    const realProps = computed(() => {
      return {
        ...props,
        treeProps: { children: 'children', hasChildren: 'hasChildren' },
        row<PERSON>ey: 'id',
        // defaultExpandAll: true,
      }
    })
    return () => (
      <NeTable ref={tableRef} {...realProps.value}>
        {{
          ...slots,
        }}
      </NeTable>
    )
  },
})

export default NeTreeTable
