import { buildProps } from '@neue-plus/utils'
import type { ExtractPropTypes } from 'vue'

export const widgetBomTableProps = buildProps({} as const)

export type WidgetBomTable = ExtractPropTypes<typeof widgetBomTableProps>

// 定义 ProTable 的 valueType 枚举
export enum ValueTypeEmu {
  Text = 'text',
  Textarea = 'textarea',
  Money = 'money',
  Digit = 'digit',
  Index = 'index',
  IndexBorder = 'indexBorder',
  Percent = 'percent',
  Date = 'date',
  DateWeek = 'dateWeek',
  DateMonth = 'dateMonth',
  DateQuarter = 'dateQuarter',
  DateYear = 'dateYear',
  DateRange = 'dateRange',
  DateTime = 'dateTime',
  DateTimeRange = 'dateTimeRange',
  Time = 'time',
  TimeRange = 'timeRange',
  FromNow = 'fromNow',
  Progress = 'progress',
  Code = 'code',
  Option = 'option',

  // 高级类型
  Select = 'select',
  Checkbox = 'checkbox',
  Radio = 'radio',
  Switch = 'switch',
  Rate = 'rate',
  Slider = 'slider',
  Avatar = 'avatar',
  Image = 'image',
  JsonCode = 'jsonCode',
}
export interface IValueEnum {
  [key: string]: {
    text: string
    status: 'Success' | 'Error' | 'Processing' | 'Warning' | 'Default'
  }
}
// 事件类型
export type WidgetBomTableEmits = {
  (e: 'beforeRender', node: any): void
  (e: 'afterRender', vnode: any, node: any): void
}
