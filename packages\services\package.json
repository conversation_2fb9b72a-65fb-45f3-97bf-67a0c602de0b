{"name": "@neue-plus/services", "version": "0.0.5", "license": "MIT", "main": "index.ts", "module": "index.ts", "unpkg": "index.js", "jsdelivr": "index.js", "types": "index.d.ts", "scripts": {"build": "tsup", "dev": "tsup --watch", "test": "vitest"}, "peerDependencies": {"@odata/client": "^2.21.10", "axios": "^1.6.0", "element-plus": "^2.0.0"}, "peerDependenciesMeta": {"element-plus": {"optional": true}}, "dependencies": {"lodash-unified": "^1.0.2"}, "devDependencies": {"@types/node": "^22.9.0", "tsup": "^8.4.0", "typescript": "^5.0.0", "vitest": "^2.0.5"}, "engines": {"node": ">=16"}}