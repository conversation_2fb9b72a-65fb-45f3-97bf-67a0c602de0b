## 1、创建 Widget

1.1 在./packages/widget 下新增 Widget 文件夹，命名 Widget 名称，如写一个 bom 的列表 Widget，那应该是 bom-table
1.2 创建 Widget 后，目录结构参考如下：
bom-table/
├── src/
│ ├── table.vue
│ ├── type.ts【参考配置】
├── style/
│ ├── css.ts
│ └── index.ts【参考配置】
├── index.ts 【参考配置】

1.3 在 packages/widget/index.ts 下增加导出

## 2、编写 vue 组件

```vue
<template>
  <widget-wrapper> 这里是你的widge组件代码 </widget-wrapper>
</template>
```

## 3、配置

在以下文件新增该组件
packages > components > material-render > src > utils > index.ts
packages > neue-plus > component.ts
packages > widgets > registry.ts

## 4、编译 Widget

在 packages > resolver 下执行 `pnpm build`

## 5、demo 中调用与调试

在 play/src/APP.vue 下新增组件调用，编写 mock 数据
在项目根目录下，执行 `pnpm dev`
