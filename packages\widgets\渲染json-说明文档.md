```ts
interface PageData {
  config: {
    configProvider?: Record<string, any>
    events?: ElementEvent[]
    api?: Record<string, any>
  }
  events: PageEvents
  apis: {
    [key: string]: SchemaApiConfig
  }
  elements: SchemaElement[]
}
```

配置项字段说明
| 字段名 | 类型 | 说明 |
|------------|------------------------|--------------------------------------------------|
| `config` | `Record<string, any>` | 配置项，用于配置全局样式，包含全局样式 `configProvider`，事件列表，API 列表 |
| `events` | `ElementEvent[]` | 事件列表 |
| `api` | `Record<string, any>` | API 列表 |
| `elements` | `SchemaElement[]` | 元素列表 |
| `ElementEvent` | 见[ElementEvent](#elementevent) | 事件对象结构，需定义具体的属性和类型 |

configProvider 配置项字段说明
配置项继承 element-plus 的 configProvider 配置项
| 属性名 | 说明 | 类型 | 默认值 |
|------------------------|--------------------------------------------|------------------------------------|-----------|
| `locale` | 翻译文本对象 | `object` | `languages: en` |
| `size` | 全局组件大小 | `enum` | `default` |
| `zIndex` | 全局初始化 `zIndex` 的值 | `number` | — |
| `namespace` | 全局组件类名称前缀 (需要配合 `$namespace` 使用) | `string` | `el` |
| `button` | 按钮相关配置，element-plus 文档 | `object` | element-plus 文档 |
| `link` | 链接相关的配置，element-plus 文档 | `object` | element-plus 文档 |
| `message` | 消息相关配置，element-plus 文档 | `object` | element-plus 文档 |
| `experimental-features`| 将要添加的实验阶段的功能，所有功能都是默认设置为 `false` | `object` | — |
| `empty-values` (2.7.0) | 输入类组件空值 | `array` | — |
| `value-on-clear` (2.7.0)| 输入类组件清空值 | `string` / `number` / `boolean` / `Function` | — |

```ts
interface ElementEvent {
  nickName: string
  eventName: string
  actions: ActionNode[]
}
```

ElementEvent 说明：
| 属性名 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| nickName | 事件名称 | string | - |
| eventName | 事件名称 | string | - |
| actions | 事件动作 | ActionNode[] | - |

```ts
interface ActionNode {
  id: string // 节点id
  type: 'start' | 'end' | 'normal' // 节点类型
  title: string // 节点标题
  content?: string // 节点内容
  config?: {
    actionType: string // 行为类型
    actionName: string // 行为名称
    target: string // 目标组件
    delay?: number // 延迟时间
    [key: string]: any
  }
}
```

ActionNode 说明：
| 属性名 | 说明 | 类型 | 默认值 |
|--------|----------|-----------------------------------------------------------|------|
| `id` | 节点 id | `string` | — |
| `type` | 节点类型 | `'start'` \| `'end'` \| `'normal'` | — |
| `title`| 节点标题 | `string` | — |
| `content`| 节点内容 | `string` | — |
| `config`| 节点配置 | `{ actionType: string; actionName: string; target: string; delay?: number; [key: string]: any; }` | — |

```ts
interface PageEvents {
  onBeforeMount?: () => void
  onMounted?: () => void
  //vue生命周期函数
}
```

```ts
export interface SchemaElement {
  id: string
  type: string
  name?: string
  props?: BaseProps
  slots?: Slot
  elements?: SchemaElement[]
  events?: ElementEvent[]
}
```

SchemaElement 说明：
| 属性名 | 说明 | 类型 | 默认值 |
|------------|--------------------|-------------------------------------|------|
| `id` | 元素的唯一标识符 | `string` | — |
| `type` | 元素类型 | `string` | — |
| `name` | 元素名称（可选） | `string` | — |
| `props` | 元素的属性（可选） | `BaseProps` | — |
| `slots` | 元素的插槽（可选） | `Slot` | — |
| `elements` | 子元素（可选） | `SchemaElement[]` | — |
| `events` | 事件列表（可选） | `ElementEvent[]` | — |
