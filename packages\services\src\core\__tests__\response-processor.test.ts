/**
 * 统一响应数据处理器测试
 */

import { describe, expect, it } from 'vitest'
import { DataType, ResponseProcessor } from '../response-processor'
import type { ApiConfig } from '../types'

describe('ResponseProcessor', () => {
  describe('analyzeResponse', () => {
    it('should identify single record', () => {
      const data = { id: 1, name: '<PERSON>' }
      const analysis = ResponseProcessor.analyzeResponse(data)

      expect(analysis.dataType).toBe(DataType.SINGLE)
      expect(analysis.isArray).toBe(false)
      expect(analysis.itemCount).toBe(1)
      expect(analysis.hasMetadata).toBe(false)
    })

    it('should identify collection data', () => {
      const data = [
        { id: 1, name: '<PERSON>' },
        { id: 2, name: '<PERSON>' },
      ]
      const analysis = ResponseProcessor.analyzeResponse(data)

      expect(analysis.dataType).toBe(DataType.COLLECTION)
      expect(analysis.isArray).toBe(true)
      expect(analysis.itemCount).toBe(2)
      expect(analysis.hasMetadata).toBe(false)
    })

    it('should identify empty data', () => {
      const analysis1 = ResponseProcessor.analyzeResponse(null)
      const analysis2 = ResponseProcessor.analyzeResponse([])

      expect(analysis1.dataType).toBe(DataType.EMPTY)
      expect(analysis2.dataType).toBe(DataType.EMPTY)
    })

    it('should identify OData format', () => {
      const data = {
        '@odata.context': 'https://example.com/$metadata#People',
        '@odata.count': 100,
        value: [{ id: 1, name: 'John' }],
      }
      const analysis = ResponseProcessor.analyzeResponse(data)

      expect(analysis.dataType).toBe(DataType.COLLECTION)
      expect(analysis.hasMetadata).toBe(true)
      expect(analysis.hasPagination).toBe(true)
      expect(analysis.itemCount).toBe(1)
    })
  })

  describe('inferDataTypeByMethod', () => {
    it('should infer correct types for HTTP methods', () => {
      expect(ResponseProcessor.inferDataTypeByMethod('get')).toBe(
        DataType.UNKNOWN
      )
      expect(ResponseProcessor.inferDataTypeByMethod('post')).toBe(
        DataType.SINGLE
      )
      expect(ResponseProcessor.inferDataTypeByMethod('put')).toBe(
        DataType.SINGLE
      )
      expect(ResponseProcessor.inferDataTypeByMethod('patch')).toBe(
        DataType.SINGLE
      )
      expect(ResponseProcessor.inferDataTypeByMethod('delete')).toBe(
        DataType.EMPTY
      )
    })
  })

  describe('inferDataTypeByUrl', () => {
    it('should identify single record URLs', () => {
      const singleUrls = [
        '/api/users/123',
        '/api/users/john-doe',
        "/api/People('key')",
        '/api/users/550e8400-e29b-41d4-a716-446655440000',
      ]

      singleUrls.forEach((url) => {
        expect(ResponseProcessor.inferDataTypeByUrl(url)).toBe(DataType.SINGLE)
      })
    })

    it('should identify collection URLs', () => {
      const collectionUrls = [
        '/api/users',
        '/api/users?page=1',
        '/api/departments/1/users',
      ]

      collectionUrls.forEach((url) => {
        expect(ResponseProcessor.inferDataTypeByUrl(url)).toBe(
          DataType.COLLECTION
        )
      })
    })
  })

  describe('extractData', () => {
    it('should extract data from OData format', () => {
      const rawData = {
        '@odata.context': 'test',
        value: [{ id: 1, name: 'John' }],
      }
      const analysis = ResponseProcessor.analyzeResponse(rawData)
      const extracted = ResponseProcessor.extractData(rawData, analysis)

      expect(extracted).toEqual([{ id: 1, name: 'John' }])
    })

    it('should use custom collection path', () => {
      const rawData = {
        data: {
          items: [{ id: 1, name: 'John' }],
        },
      }
      const analysis = ResponseProcessor.analyzeResponse(rawData)
      const extracted = ResponseProcessor.extractData(rawData, analysis, {
        collectionPath: 'data.items',
      })

      expect(extracted).toEqual([{ id: 1, name: 'John' }])
    })

    it('should use custom extractor', () => {
      const rawData = { result: { user: { id: 1, name: 'John' } } }
      const analysis = ResponseProcessor.analyzeResponse(rawData)
      const extracted = ResponseProcessor.extractData(rawData, analysis, {
        customExtractor: (data) => data.result.user,
      })

      expect(extracted).toEqual({ id: 1, name: 'John' })
    })
  })

  describe('processResponse', () => {
    const mockConfig: ApiConfig = {
      url: '/api/users',
      method: 'get',
      protocol: 'rest',
    }

    it('should process single record response', () => {
      const rawData = { id: 1, name: 'John Doe' }
      const response = ResponseProcessor.processResponse(rawData, mockConfig)

      expect(response.success).toBe(true)
      expect(response.data).toEqual(rawData)
      expect(response.total).toBe(1)
    })

    it('should process collection response', () => {
      const rawData = [
        { id: 1, name: 'John' },
        { id: 2, name: 'Jane' },
      ]
      const response = ResponseProcessor.processResponse(rawData, mockConfig)

      expect(response.success).toBe(true)
      expect(response.data).toEqual(rawData)
      expect(response.pageSize).toBe(2)
    })

    it('should process OData response with pagination', () => {
      const rawData = {
        '@odata.count': 100,
        '@odata.nextLink': 'next-page-url',
        value: [{ id: 1, name: 'John' }],
      }
      const response = ResponseProcessor.processResponse(rawData, mockConfig)

      expect(response.success).toBe(true)
      expect(response.data).toEqual([{ id: 1, name: 'John' }])
      expect(response.total).toBe(100)
      expect(response.hasNext).toBe(true)
    })

    it('should handle errors gracefully', () => {
      const mockConfigWithError: ApiConfig = {
        url: '/api/users',
        method: 'get',
        protocol: 'rest',
      }

      // 模拟处理过程中的错误
      const response = ResponseProcessor.processResponse(
        undefined,
        mockConfigWithError,
        {
          customExtractor: () => {
            throw new Error('Test error')
          },
        }
      )

      expect(response.success).toBe(false)
      expect(response.error).toBe('Test error')
    })
  })

  describe('convenience methods', () => {
    const mockConfig: ApiConfig = {
      url: '/api/users/123',
      method: 'get',
      protocol: 'rest',
    }

    it('should process GET response', () => {
      const rawData = { id: 123, name: 'John' }
      const response = ResponseProcessor.processGetResponse(rawData, mockConfig)

      expect(response.success).toBe(true)
      expect(response.data).toEqual(rawData)
    })

    it('should process POST response', () => {
      const rawData = { id: 123, name: 'John' }
      const response = ResponseProcessor.processPostResponse(rawData, {
        ...mockConfig,
        method: 'post',
      })

      expect(response.success).toBe(true)
      expect(response.data).toEqual(rawData)
    })

    it('should process UPDATE response', () => {
      const rawData = { id: 123, name: 'John Updated' }
      const response = ResponseProcessor.processUpdateResponse(rawData, {
        ...mockConfig,
        method: 'put',
      })

      expect(response.success).toBe(true)
      expect(response.data).toEqual(rawData)
    })

    it('should process DELETE response', () => {
      const rawData = true
      const response = ResponseProcessor.processDeleteResponse(rawData, {
        ...mockConfig,
        method: 'delete',
      })

      expect(response.success).toBe(true)
    })
  })
})
