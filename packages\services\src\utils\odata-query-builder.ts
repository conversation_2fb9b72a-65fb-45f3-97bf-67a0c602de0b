/**
 * OData 查询构建器
 * 提供链式调用的方式构建 OData 查询
 */

import type {
  FilterOperator,
  ODataFilterRule,
  ODataQueryOptions,
} from '../core/types'

/**
 * OData 查询构建器类
 */
export class ODataQueryBuilder {
  private options: ODataQueryOptions = {}
  private filterRules: ODataFilterRule[] = []

  /**
   * 添加过滤条件
   */
  filter(
    field: string,
    value: string | number | boolean,
    operator: FilterOperator = 'eq'
  ): this {
    this.filterRules.push({ field, value, operator })
    return this
  }

  /**
   * 添加 AND 过滤条件
   */
  and(
    field: string,
    value: string | number | boolean,
    operator: FilterOperator = 'eq'
  ): this {
    this.filterRules.push({ field, value, operator, logical: 'and' })
    return this
  }

  /**
   * 添加 OR 过滤条件
   */
  or(
    field: string,
    value: string | number | boolean,
    operator: FilterOperator = 'eq'
  ): this {
    this.filterRules.push({ field, value, operator, logical: 'or' })
    return this
  }

  /**
   * 添加等于条件
   */
  eq(field: string, value: string | number | boolean): this {
    return this.filter(field, value, 'eq')
  }

  /**
   * 添加不等于条件
   */
  ne(field: string, value: string | number | boolean): this {
    return this.filter(field, value, 'ne')
  }

  /**
   * 添加包含条件
   */
  contains(field: string, value: string): this {
    return this.filter(field, value, 'contains')
  }

  /**
   * 添加开始于条件
   */
  startsWith(field: string, value: string): this {
    return this.filter(field, value, 'startswith')
  }

  /**
   * 添加结束于条件
   */
  endsWith(field: string, value: string): this {
    return this.filter(field, value, 'endswith')
  }

  /**
   * 添加大于条件
   */
  gt(field: string, value: number): this {
    return this.filter(field, value, 'gt')
  }

  /**
   * 添加大于等于条件
   */
  ge(field: string, value: number): this {
    return this.filter(field, value, 'ge')
  }

  /**
   * 添加小于条件
   */
  lt(field: string, value: number): this {
    return this.filter(field, value, 'lt')
  }

  /**
   * 添加小于等于条件
   */
  le(field: string, value: number): this {
    return this.filter(field, value, 'le')
  }

  /**
   * 选择字段
   */
  select(fields: string | string[]): this {
    this.options.select = Array.isArray(fields) ? fields.join(',') : fields
    return this
  }

  /**
   * 展开关联数据
   */
  // expand(relations: string | string[]): this {
  //   this.options.expand = Array.isArray(relations)
  //     ? relations.join(',')
  //     : relations
  //   return this
  // }

  /**
   * 排序
   */
  orderBy(field: string, direction: 'asc' | 'desc' = 'asc'): this {
    const orderByValue = direction === 'desc' ? `${field} desc` : field
    if (this.options.orderby) {
      this.options.orderby += `, ${orderByValue}`
    } else {
      this.options.orderby = orderByValue
    }
    return this
  }

  /**
   * 限制返回数量
   */
  top(count: number): this {
    this.options.top = count
    return this
  }

  /**
   * 跳过指定数量
   */
  skip(count: number): this {
    this.options.skip = count
    return this
  }

  /**
   * 启用计数
   */
  count(enable: boolean = true): this {
    this.options.count = enable
    return this
  }

  /**
   * 搜索
   */
  search(term: string): this {
    this.options.search = term
    return this
  }

  /**
   * 构建查询选项
   */
  build(): ODataQueryOptions {
    const result = { ...this.options }

    // 如果有过滤规则，构建过滤字符串
    if (this.filterRules.length > 0) {
      result.filter = this.filterRules
    }

    return result
  }

  /**
   * 重置构建器
   */
  reset(): this {
    this.options = {}
    this.filterRules = []
    return this
  }

  /**
   * 克隆构建器
   */
  clone(): ODataQueryBuilder {
    const cloned = new ODataQueryBuilder()
    cloned.options = { ...this.options }
    cloned.filterRules = [...this.filterRules]
    return cloned
  }
}

/**
 * 创建新的查询构建器实例
 */
export function createODataQuery(): ODataQueryBuilder {
  return new ODataQueryBuilder()
}

/**
 * 便捷的查询构建器别名
 */
export const query = createODataQuery

/**
 * 示例用法：
 *
 * // 基本查询
 * const options = query()
 *   .select(['FirstName', 'LastName', 'Email'])
 *   .contains('FirstName', 'John')
 *   .or('LastName', 'Doe', 'contains')
 *   .orderBy('FirstName')
 *   .top(10)
 *   .build()
 *
 * // 复杂查询
 * const complexOptions = query()
 *   .select('FirstName,LastName,Age,Email')
 *   .contains('FirstName', 'John')
 *   .or('Email', 'john', 'contains')
 *   .and('Age', 18, 'gt')
 *   .and('IsActive', true)
 *   .orderBy('FirstName', 'asc')
 *   .orderBy('LastName', 'desc')
 *   .top(20)
 *   .skip(10)
 *   .count(true)
 *   .build()
 *
 * // 使用构建的选项进行查询
 * const result = await executeApi({
 *   url: 'https://services.odata.org/V4/TripPinServiceRW/People',
 *   method: 'get',
 *   protocol: 'odata',
 *   query: options
 * })
 */
