# pageData 低代码渲染配置文件说明

## 1. 简介

`pageData` 文件是用于低代码平台页面渲染的核心配置文件。通过 JSON 或 JS 对象的形式，描述页面的结构、组件、数据源、交互逻辑等，实现页面的动态生成和渲染。

---

## 2. 主要结构

一个典型的 `pageData` 配置包含以下几个部分：

- `type`：页面类型（如 page、form、table 等）
- `title`：页面标题
- `props`：页面级属性
- `children`：页面内的组件树
- `dataSource`：数据源配置
- `actions`：页面级事件与动作

---

## 3. 字段说明

| 字段       | 类型         | 说明           |
| ---------- | ------------ | -------------- |
| type       | string       | 页面或组件类型 |
| title      | string       | 页面标题       |
| props      | object       | 页面或组件属性 |
| children   | array        | 子组件配置     |
| dataSource | object/array | 数据源配置     |
| actions    | object/array | 事件与动作配置 |

---

## 4. 典型示例

### 4.1 基础页面（表单+表格）

```jsonc
{
  "type": "page",
  "title": "用户管理",
  "props": {
    "style": { "padding": "24px" }
  },
  "dataSource": {
    "users": {
      "type": "api",
      "url": "/api/users",
      "method": "GET"
    }
  },
  "children": [
    {
      "type": "form",
      "props": {
        "layout": "inline"
      },
      "children": [
        {
          "id": "searchForm_1",
          "type": "input",
          "props": {
            "name": "username",
            "placeholder": "请输入用户名"
          },
          "events": []
        },
        {
          "type": "div",
          "children": [
            {
              "config": {
                "actionType": "toggleExpandAll",
                "target": "TableComponent_1",
                "delay": 300
              }
            }
          ]
        },
        {
          "type": "select",
          "props": {
            "name": "username",
            "placeholder": "请输入用户名"
          }
        },
        {
          "type": "button",
          "props": {
            "type": "primary",
            "text": "搜索"
          },
          "actions": {
            "onClick": "search"
          }
        }
      ]
    },
    {
      "type": "table",
      "props": {
        "dataSource": "{{users}}",
        "columns": [
          { "title": "ID", "dataIndex": "id" },
          { "title": "用户名", "dataIndex": "username" },
          { "title": "邮箱", "dataIndex": "email" }
        ]
      }
    }
  ],
  "actions": {
    "search": {
      "type": "reload",
      "target": "users"
    }
  }
}
```

**说明：**

- 该页面包含一个搜索表单和一个用户表格。
- 表单输入用户名后点击"搜索"按钮，会触发 `search` 动作，刷新 `users` 数据源。
- 表格的数据来源于 `dataSource.users`。

---

### 4.2 表单校验与联动

```jsonc
{
  "type": "form",
  "title": "注册",
  "props": {
    "labelWidth": "100px"
  },
  "children": [
    {
      "type": "input",
      "props": {
        "name": "email",
        "label": "邮箱",
        "rules": [
          { "required": true, "message": "请输入邮箱" },
          { "type": "email", "message": "邮箱格式不正确" }
        ]
      }
    },
    {
      "type": "input",
      "props": {
        "name": "password",
        "label": "密码",
        "type": "password",
        "rules": [
          { "required": true, "message": "请输入密码" },
          { "min": 6, "message": "密码至少6位" }
        ]
      }
    },
    {
      "type": "select",
      "props": {
        "name": "role",
        "label": "角色",
        "options": [
          { "label": "管理员", "value": "admin" },
          { "label": "普通用户", "value": "user" }
        ]
      }
    },
    {
      "type": "input",
      "props": {
        "name": "adminCode",
        "label": "管理员邀请码",
        "visible": "{{form.role === 'admin'}}"
      }
    },
    {
      "type": "button",
      "props": {
        "type": "primary",
        "text": "注册"
      },
      "actions": {
        "onClick": "submit"
      }
    }
  ],
  "actions": {
    "submit": {
      "type": "submit",
      "api": "/api/register"
    }
  }
}
```

**说明：**

- 表单包含邮箱、密码、角色、管理员邀请码等字段。
- `rules` 字段用于表单校验。
- `adminCode` 字段通过 `visible` 实现联动，仅当角色为"管理员"时显示。
- 点击"注册"按钮会触发 `submit` 动作，提交表单到指定 API。

---

### 4.3 静态数据源与下拉联动

```jsonc
{
  "type": "form",
  "title": "省市选择",
  "dataSource": {
    "provinces": [
      { "label": "广东", "value": "gd" },
      { "label": "浙江", "value": "zj" }
    ],
    "cities": {
      "gd": [
        { "label": "广州", "value": "gz" },
        { "label": "深圳", "value": "sz" }
      ],
      "zj": [
        { "label": "杭州", "value": "hz" },
        { "label": "宁波", "value": "nb" }
      ]
    }
  },
  "children": [
    {
      "type": "select",
      "props": {
        "name": "province",
        "label": "省份",
        "options": "{{provinces}}"
      }
    },
    {
      "type": "select",
      "props": {
        "name": "city",
        "label": "城市",
        "options": "{{cities[form.province]}}",
        "disabled": "{{!form.province}}"
      }
    }
  ]
}
```

**说明：**

- `dataSource` 里定义了静态的省份和城市数据。
- 城市下拉框的选项和禁用状态会根据省份选择动态变化。

---

### 4.4 复杂嵌套与自定义组件

```jsonc
{
  "type": "page",
  "title": "仪表盘",
  "children": [
    {
      "type": "row",
      "children": [
        {
          "type": "col",
          "props": { "span": 12 },
          "children": [
            {
              "type": "custom-chart",
              "props": {
                "title": "访问量趋势",
                "data": "{{chartData}}"
              }
            }
          ]
        },
        {
          "type": "col",
          "props": { "span": 12 },
          "children": [
            {
              "type": "table",
              "props": {
                "dataSource": "{{tableData}}",
                "columns": [
                  { "title": "日期", "dataIndex": "date" },
                  { "title": "访问量", "dataIndex": "pv" }
                ]
              }
            }
          ]
        }
      ]
    }
  ],
  "dataSource": {
    "chartData": {
      "type": "api",
      "url": "/api/chart"
    },
    "tableData": {
      "type": "api",
      "url": "/api/table"
    }
  }
}
```

**说明：**

- 页面通过 row/col 实现布局嵌套。
- 支持自定义组件（如 custom-chart）。
- 数据源可为多个 API。

---

## 5. 详细说明

- `type` 决定了当前节点的渲染类型。
- `children` 支持递归嵌套，实现复杂页面结构。
- `dataSource` 支持 API、静态数据等多种数据源。
- `actions` 可定义交互逻辑，如按钮点击、表单提交等。
- `props` 支持所有组件原生属性，也可扩展自定义属性。
- `visible`、`disabled` 等属性支持表达式，实现联动与动态控制。
- `options` 字段支持静态数组、数据源变量、表达式等多种写法。

---

## 6. 扩展用法

- 支持自定义组件类型，只需在 `type` 字段指定组件名。
- `props` 可传递任意组件支持的属性。
- `actions` 可与平台内置事件系统联动，实现复杂交互。
- 支持表达式（如 `{{变量}}`、`{{form.xxx}}`）实现数据绑定和联动。

---

如需更详细的字段说明或扩展用法，请补充你的具体需求！
