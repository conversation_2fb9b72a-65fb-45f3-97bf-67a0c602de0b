/**
 * 初始化状态管理模块
 * 避免循环依赖，独立管理初始化状态
 */

import { getGlobalContainer } from './container'

/**
 * 检查框架是否已初始化
 */
export function isInitialized(): boolean {
  const container = getGlobalContainer()
  return container.has('rest-adapter') && container.has('odata-adapter')
}

/**
 * 自动初始化（如果尚未初始化）
 * 使用延迟导入避免循环依赖
 */
export async function autoInitialize(): Promise<void> {
  if (!isInitialized()) {
    // 延迟导入 bootstrap 模块，避免循环依赖
    const { quickStart } = await import('./bootstrap')
    await quickStart()
  }
}

/**
 * 获取框架状态
 */
export function getInitializationStatus(): {
  isInitialized: boolean
  hasRestAdapter: boolean
  hasODataAdapter: boolean
} {
  const container = getGlobalContainer()
  return {
    isInitialized: isInitialized(),
    hasRestAdapter: container.has('rest-adapter'),
    hasODataAdapter: container.has('odata-adapter'),
  }
}
