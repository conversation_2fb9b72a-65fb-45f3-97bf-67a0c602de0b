# Changelog

## v1.0.0 - OData 客户端框架重构

### 🚀 新特性

#### 强大的 OData 客户端

- **新增** `createODataClient` 函数，提供完整的 OData 客户端功能
- **新增** `ODataClient` 类，支持完整的 CRUD 操作
- **新增** 客户端缓存机制，自动复用相同 baseUrl 的客户端实例
- **新增** 认证支持（Basic、Bearer、OAuth2）
- **新增** 函数和操作调用支持

#### 增强的类型系统

- **新增** `ODataServiceConfig` 接口，完整的客户端配置
- **新增** `ODataQueryOptions` 接口，支持所有 OData 查询选项
- **新增** `PaginatedResponse` 接口，标准化的分页响应
- **新增** `ODataEntity` 和 `ODataResponse` 类型定义

#### 改进的请求处理

- **重构** `request.ts`，提供新的 `request` 函数和向后兼容的默认导出
- **新增** `RequestConfig` 接口，统一的请求配置
- **改进** 错误处理，提供更详细的错误信息

### 🔧 改进

#### API 执行器增强

- **改进** `executeApi` 函数，智能选择使用新的 OData 客户端或传统实现
- **保持** 向后兼容性，现有代码无需修改
- **优化** OData 查询性能和功能

#### 代码清理

- **移除** 冗余的 `buildRestParams` 函数（功能已被 `parseExpression` 覆盖）
- **移除** 不必要的 `restAdapter.ts` 文件
- **清理** 测试文件，移除冗余测试

### 📦 导出更新

#### 新增导出

```typescript
// OData 客户端
export { createODataClient, ODataClient } from './src/odataClient'

// 新的请求函数
export { request } from './src/request'

// 新的类型定义
export type {
  ODataQueryOptions,
  ODataServiceConfig,
  ODataEntity,
  ODataResponse,
  PaginatedResponse,
  RequestConfig,
} from './src/types'
```

#### 移除导出

```typescript
// 已移除（功能冗余）
export { buildRestParams } from './src/adapters/restAdapter'
```

### 🎯 使用示例

#### 新的 OData 客户端用法

```typescript
import { createODataClient } from '@neue-plus/services'

// 创建客户端
const client = createODataClient({
  baseUrl: 'https://services.odata.org/V4/TripPinServiceRW',
  timeout: 30000,
  auth: {
    type: 'bearer',
    credentials: { token: 'your-token' },
  },
})

// 查询数据
const result = await client.query('People', {
  select: 'FirstName,LastName,Gender',
  filter: 'Gender eq "Female"',
  orderby: 'FirstName asc',
  top: 10,
  skip: 0,
  count: true,
})

// CRUD 操作
const person = await client.get('russellwhyte', 'People')
const newPerson = await client.create(
  { FirstName: 'John', LastName: 'Doe' },
  'People'
)
const updated = await client.update(
  'johnDoe',
  { FirstName: 'Johnny' },
  'People'
)
const deleted = await client.delete('johnDoe', 'People')
```

#### 向后兼容的 executeApi 用法

```typescript
import { executeApi } from '@neue-plus/services'

// 原有代码无需修改，自动使用新的 OData 客户端
const result = await executeApi({
  url: 'https://services.odata.org/V4/TripPinServiceRW/People',
  method: 'get',
  protocol: 'odata',
  odata: {
    select: 'FirstName,LastName,Gender',
    top: 5,
    filter: 'Gender eq "Female"',
  },
  dataPath: 'value',
})
```

### 🛡️ 向后兼容性

- ✅ 所有现有的 `executeApi` 调用继续正常工作
- ✅ 所有现有的类型定义保持不变
- ✅ 所有现有的错误处理机制保持不变
- ✅ 所有现有的表达式解析功能保持不变

### 📈 性能改进

- **客户端缓存**: 相同 baseUrl 的客户端实例会被自动复用
- **更好的错误处理**: 减少不必要的错误重试
- **优化的请求处理**: 新的 request 函数提供更好的性能

### 🔄 迁移指南

#### 对于现有用户

无需任何迁移工作，所有现有代码继续正常工作。

#### 对于新功能

推荐使用新的 OData 客户端来获得更强大的功能：

```typescript
// 旧方式（仍然支持）
const result = await executeApi(apiSchema, context)

// 新方式（推荐）
const client = createODataClient(config)
const result = await client.query('EntitySet', options, context)
```

### 🧪 测试

- ✅ 新增 OData 客户端的完整测试套件
- ✅ 保持所有现有测试的通过
- ✅ 移除冗余的 REST 适配器测试

### 📝 文档

- ✅ 更新 README.md，包含新的 OData 客户端用法
- ✅ 添加完整的 API 文档和示例
- ✅ 提供迁移指南和最佳实践

---

这次重构大大增强了 `@neue-plus/services` 的 OData 支持能力，同时保持了完全的向后兼容性。新的 OData 客户端提供了更强大、更灵活的 API，适合复杂的企业级应用场景。
