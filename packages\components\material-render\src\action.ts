// types/action.ts

import { ActionNode } from '../types'

export type ActionType =
  | 'log'
  | 'message'
  | 'callback'
  | 'confirm'
  | 'notification'
  | 'request'
  | 'visible'
  | 'disable'
  | 'copy'
  | 'timeout'
  | 'jumpLink'
  | 'variable'
  | 'modal'
  | 'script'
// ...可扩展更多类型

export interface BaseAction {
  actionType: ActionType
  delay?: number
}

export interface LogAction extends BaseAction {
  actionType: 'log'
  content: string
}

export interface MessageAction extends BaseAction {
  actionType: 'message'
  type: 'info' | 'success' | 'warning' | 'error'
  content: string
}

export interface CallbackAction extends BaseAction {
  actionType: 'callback'
  fn: (params: any) => void
}

export interface ConfirmAction extends BaseAction {
  actionType: 'confirm'
  title: string
  content: string
  onConfirm?: () => void
  onCancel?: () => void
}

// 所有 Action 的联合类型
export type Action = LogAction | MessageAction | CallbackAction | ConfirmAction
// | ...其它类型

export interface EventFlow {
  run: (
    actions: ActionNode[],
    params?: Record<string, any>
  ) => void | Promise<void>
}
