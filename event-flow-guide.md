# 📦 EventFlow 事件流程机制

基于 Vue 3 的依赖注入和组合式 API，实现 **链式事件调用机制**，可用于低代码平台、动态组件行为控制等场景。

---

## 📁 目录结构

```bash
src/
├── context/
│   └── event-flow.ts     # 提供/注入 refs 和 handlers 的上下文
├── hooks/
│   └── useEventFlow.ts   # 核心执行器（链表结构 + 执行）
```

---

## 🔌 1. 上下文提供（`provideEventContext`）

```ts
export function provideEventContext(handlers: Record<string, any>) {
  const refs = reactive<Record<string, any>>({})
  provide(REFS_KEY, refs)
  provide(HANDLERS_KEY, handlers)
  return refs
}
```

### ✅ 用法：

```ts
const refs = provideEventContext(handlers)
```

- `handlers` 是事件行为定义对象，例如 `{ toggleExpandAll: fn }`
- `refs` 是动态组件实例的引用容器（由子组件注册）

---

## 🧩 2. 注入上下文（`useEventContext`）

```ts
export function useEventContext() {
  const refs = inject<Record<string, any>>(REFS_KEY)
  const handlers = inject<Record<string, any>>(HANDLERS_KEY)
  if (!refs || !handlers) throw new Error('Event context is not provided')
  return { refs, handlers }
}
```

在组件或 hook 中使用：

```ts
const { refs, handlers } = useEventContext()
```

---

## 🔄 3. 串行执行器（`useEventFlow`）

```ts
export function useEventFlow() {
  const { refs, handlers } = useEventContext()

  function run(actions: ActionNode[] = [], params: Record<string, any> = {}) {
    const linkedList = toLinkedList(actions)
    if (linkedList) exec(linkedList, params)
  }

  async function exec(node: EventNode | null, params: Record<string, any>) {
    const { actionType, delay = 0, target = '' } = node.action.config || {}
    const handler = refs[target]?.[actionType] || handlers[actionType]

    if (typeof handler === 'function') {
      await handler(node.action, params, refs)
    }

    await exec(node.next, params)
  }

  return { run }
}
```

---

## 🔗 事件节点结构（`ActionNode`）

```ts
interface ActionNode {
  id?: string
  config: {
    actionType: string
    target?: string
    delay?: number
    [key: string]: any
  }
}
```

```ts
const exampleActions: ActionNode[] = [
  {
    config: {
      actionType: 'toggleExpandAll',
      target: 'TableComponent_1',
      delay: 300,
    },
  },
  {
    config: {
      actionType: 'showToast',
      delay: 100,
    },
  },
]
```

---

## 🧠 refs 与 defineExpose

组件内暴露方法：

```ts
defineExpose({
  toggleExpandAll: () => {
    console.log('触发表格展开/收起')
  },
})
```

这样在事件流中，即可通过：

```ts
refs[target]?.[actionType]
```

访问组件方法。

---

## 🚨 注意事项

- 组件 `id` 必须唯一，并通过 `refs[id]` 正确注册
- 方法名应与 `actionType` 匹配
- 确保父组件已调用 `provideEventContext`

---

## ✅ 示例调用

```ts
eventFlow.run([
  {
    config: {
      actionType: 'toggleExpandAll',
      target: 'Table_123',
    },
  },
  {
    config: {
      actionType: 'showDialog',
      target: 'Dialog_456',
      delay: 300,
    },
  },
])
```

---

## 📚 总结

| 功能                 | 实现方式                  |
| -------------------- | ------------------------- |
| 注入组件方法         | `defineExpose + refs`     |
| 串行执行事件         | `useEventFlow + exec链表` |
| 自定义行为定义       | handlers + context 提供   |
| 延迟、目标、动态组件 | 全部内建支持              |
