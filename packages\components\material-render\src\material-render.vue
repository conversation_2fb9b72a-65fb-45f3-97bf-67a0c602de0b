<template>
  <NeConfigProvider v-bind="configProviderConfig">
    <template v-if="elements && elements.length > 0">
      <NeRenderCore
        v-for="element in elements"
        :key="element.id"
        v-bind="element"
        @before-render="handleBeforeRender"
        @after-render="handleAfterRender"
      />
    </template>
  </NeConfigProvider>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import NeConfigProvider from '../../config-provider/src/config-provider.vue'
import NeRenderCore from './render-core/render-core'
import { neMaterialRenderProps } from './types'
import { provideEventContext } from './context/event-flow'
import * as handlers from './handlers'

defineOptions({
  name: 'NeMaterialRender',
})
const props = defineProps(neMaterialRenderProps)
provideEventContext(handlers, props.apis)
console.log(props)
const configProviderConfig = computed(() => props.config?.configProvider)
// const events = computed(() => props.events)
const elements = computed(() => {
  return props.elements
})
const emit = defineEmits(['beforeRender', 'afterRender'])
const handleBeforeRender = (node: any) => {
  emit('beforeRender', node)
}
const handleAfterRender = (vnode: any, node: any) => {
  emit('afterRender', vnode, node)
}
</script>
