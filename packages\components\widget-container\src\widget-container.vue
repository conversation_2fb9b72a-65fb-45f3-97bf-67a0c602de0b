<template>
  <div class="widget-container">
    <slot />
  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue'

const widgetRefs = new Map<string, any>()

// 事件总线 - 简易版
const eventBus = reactive({
  dispatch(eventName: string, payload: any) {
    for (const ref of widgetRefs.values()) {
      ref?.handleEvent?.(eventName, payload)
    }
  },
})

function getAllWidgetMethods() {
  const result: Record<string, any> = {}
  for (const [id, ref] of widgetRefs.entries()) {
    result[id] = ref?.getMethods?.() ?? {}
  }
  return result
}

// 暴露容器方法
defineExpose({
  getAllWidgetMethods,
  eventBus,
})
</script>
