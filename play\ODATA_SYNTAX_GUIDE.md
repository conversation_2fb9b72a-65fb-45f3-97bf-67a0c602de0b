# 📚 OData 查询语法指南

## 概述

OData (Open Data Protocol) 是一个用于构建和使用 RESTful API 的标准协议。本指南介绍在 ApiRequestButton 组件中使用 OData 查询的正确语法。

## 🔍 基本查询选项

### 1. $select - 字段选择

选择要返回的字段，用逗号分隔。

```odata
$select: FirstName,LastName,Gender
$select: UserName,Emails
$select: *  // 选择所有字段
```

### 2. $filter - 数据过滤

根据条件过滤数据。

#### 字符串比较

```odata
$filter: FirstName eq 'John'
$filter: LastName ne 'Doe'
$filter: contains(FirstName, 'Jo')
$filter: startswith(LastName, 'Do')
$filter: endswith(Email, 'gmail.com')
```

#### 数值比较

```odata
$filter: Age eq 25
$filter: Age gt 18
$filter: Age lt 65
$filter: Age ge 21
$filter: Age le 60
```

#### 逻辑运算

```odata
$filter: FirstName eq 'John' and LastName eq 'Doe'
$filter: Age gt 18 or Age lt 65
$filter: not (FirstName eq 'John')
```

### 3. $orderby - 排序

指定排序字段和方向。

```odata
$orderby: FirstName
$orderby: FirstName asc
$orderby: LastName desc
$orderby: FirstName asc, LastName desc
```

### 4. $top - 限制数量

限制返回的记录数量。

```odata
$top: 10
$top: 50
```

### 5. $skip - 跳过记录

跳过指定数量的记录（用于分页）。

```odata
$skip: 0   // 第一页
$skip: 10  // 跳过前10条
$skip: 20  // 跳过前20条
```

## ⚠️ 重要语法规则

### 1. 字符串值使用单引号

```odata
✅ 正确: Gender eq 'Female'
❌ 错误: Gender eq "Female"
❌ 错误: Gender eq Female
```

### 2. 数值不需要引号

```odata
✅ 正确: Age eq 25
❌ 错误: Age eq '25'
```

### 3. 布尔值不需要引号

```odata
✅ 正确: IsActive eq true
✅ 正确: IsDeleted eq false
❌ 错误: IsActive eq 'true'
```

### 4. 日期时间格式

```odata
✅ 正确: CreatedDate eq 2023-01-01T00:00:00Z
✅ 正确: CreatedDate gt 2023-01-01
```

## 🎯 实用示例

### 示例 1: 基本用户查询

```odata
URL: https://services.odata.org/V4/TripPinServiceRW/People
$select: FirstName,LastName,UserName
$top: 10
```

### 示例 2: 过滤女性用户

```odata
URL: https://services.odata.org/V4/TripPinServiceRW/People
$select: FirstName,LastName,Gender
$filter: Gender eq 'Female'
$top: 5
```

### 示例 3: 复杂查询

```odata
URL: https://services.odata.org/V4/TripPinServiceRW/People
$select: FirstName,LastName,Gender,UserName
$filter: Gender eq 'Male' and contains(FirstName, 'R')
$orderby: FirstName asc
$top: 10
$skip: 0
```

### 示例 4: 分页查询

```odata
// 第一页 (前10条)
$top: 10
$skip: 0

// 第二页 (第11-20条)
$top: 10
$skip: 10

// 第三页 (第21-30条)
$top: 10
$skip: 20
```

## 🔧 在 ApiRequestButton 中使用

### 1. 设置协议

选择 "OData" 协议

### 2. 输入 URL

```
https://services.odata.org/V4/TripPinServiceRW/People
```

### 3. 配置 OData 选项

在 "OData" 标签页中填写：

| 字段     | 示例值                      | 说明     |
| -------- | --------------------------- | -------- |
| $select  | `FirstName,LastName,Gender` | 选择字段 |
| $filter  | `Gender eq 'Female'`        | 过滤条件 |
| $top     | `5`                         | 限制数量 |
| $skip    | `0`                         | 跳过数量 |
| $orderby | `FirstName asc`             | 排序规则 |

## 🚨 常见错误

### 1. 字符串引号错误

```odata
❌ 错误: Gender eq "Female"
✅ 正确: Gender eq 'Female'
```

### 2. 字段名拼写错误

```odata
❌ 错误: FirstNam eq 'John'  // 字段名拼写错误
✅ 正确: FirstName eq 'John'
```

### 3. 运算符错误

```odata
❌ 错误: Age = 25           // 使用了 = 而不是 eq
✅ 正确: Age eq 25

❌ 错误: Age > 18           // 使用了 > 而不是 gt
✅ 正确: Age gt 18
```

### 4. 逻辑运算符错误

```odata
❌ 错误: FirstName eq 'John' AND LastName eq 'Doe'  // 大写
✅ 正确: FirstName eq 'John' and LastName eq 'Doe'  // 小写
```

## 📖 OData 运算符参考

### 比较运算符

| 运算符 | 说明     | 示例             |
| ------ | -------- | ---------------- |
| `eq`   | 等于     | `Name eq 'John'` |
| `ne`   | 不等于   | `Name ne 'John'` |
| `gt`   | 大于     | `Age gt 18`      |
| `ge`   | 大于等于 | `Age ge 18`      |
| `lt`   | 小于     | `Age lt 65`      |
| `le`   | 小于等于 | `Age le 65`      |

### 逻辑运算符

| 运算符 | 说明   | 示例                |
| ------ | ------ | ------------------- |
| `and`  | 逻辑与 | `A eq 1 and B eq 2` |
| `or`   | 逻辑或 | `A eq 1 or B eq 2`  |
| `not`  | 逻辑非 | `not (A eq 1)`      |

### 字符串函数

| 函数         | 说明   | 示例                      |
| ------------ | ------ | ------------------------- |
| `contains`   | 包含   | `contains(Name, 'Jo')`    |
| `startswith` | 开始于 | `startswith(Name, 'Jo')`  |
| `endswith`   | 结束于 | `endswith(Email, '.com')` |
| `length`     | 长度   | `length(Name) gt 5`       |
| `tolower`    | 转小写 | `tolower(Name) eq 'john'` |
| `toupper`    | 转大写 | `toupper(Name) eq 'JOHN'` |

## 🔗 有用的测试 URL

### TripPin 服务 (推荐用于测试)

```
https://services.odata.org/V4/TripPinServiceRW/People
https://services.odata.org/V4/TripPinServiceRW/Airlines
https://services.odata.org/V4/TripPinServiceRW/Airports
```

### Northwind 服务

```
https://services.odata.org/V4/Northwind/Northwind.svc/Customers
https://services.odata.org/V4/Northwind/Northwind.svc/Products
https://services.odata.org/V4/Northwind/Northwind.svc/Orders
```

## 💡 最佳实践

1. **从简单开始**: 先测试基本的 $select 和 $top
2. **逐步添加**: 然后添加 $filter 和 $orderby
3. **验证语法**: 确保字符串使用单引号
4. **测试分页**: 使用 $top 和 $skip 实现分页
5. **查看错误**: 仔细阅读错误信息来调试问题

## 🆘 故障排除

### 语法错误

如果遇到语法错误，检查：

1. 字符串是否使用单引号
2. 字段名是否正确
3. 运算符是否使用小写
4. 括号是否匹配

### 无数据返回

如果查询无数据返回，检查：

1. 过滤条件是否过于严格
2. 字段值是否存在
3. 数据类型是否匹配

### 性能问题

如果查询较慢，考虑：

1. 减少 $top 的值
2. 简化 $filter 条件
3. 减少 $select 的字段数量
