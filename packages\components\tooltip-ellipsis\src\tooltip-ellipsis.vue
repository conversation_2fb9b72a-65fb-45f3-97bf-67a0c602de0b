<template>
  <div ref="textContent" class="ny-tooltip-ellipsis">
    <el-tooltip v-if="isOverflow" v-bind="realProps">
      <template #content>
        <content />
      </template>
      <div class="truncate">
        <slot name="default" />
      </div>
    </el-tooltip>
    <div v-else ref="textElement" style="display: inline-block">
      <slot name="default" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  computed,
  createVNode,
  onMounted,
  onUnmounted,
  ref,
  useSlots,
} from 'vue'
import { neTooltipEllipsisProps } from './types'

defineOptions({
  name: 'NeTooltipEllipsis',
})

defineProps({ ...neTooltipEllipsisProps })
const realProps = computed(() => {
  return { placement: 'top', showArrow: true }
})
const slots = useSlots()
const timer = ref()
const count = ref(0)
const textElement = ref()
const textContent = ref()
const isOverflow = ref(false)
//暂时没有启用宽度变化监听功能  性能开销很大
// const { width } = useResizeObserver(textElement)

const content = computed(() => {
  return createVNode('div', {}, slots.content?.({}) || slots.default!({}))
})
const init = () => {
  clearTimeout(timer.value)
  timer.value = setTimeout(() => {
    const { width: cWidth } = textContent.value.getBoundingClientRect()
    const { width: tWidth } = textElement.value.getBoundingClientRect()
    if (tWidth === 0) {
      count.value += 1
      if (count.value > 10) {
        clearTimeout(timer.value)
      } else {
        init()
        return
      }
    } else if (cWidth < tWidth) {
      isOverflow.value = true
    } else {
      isOverflow.value = false
    }
  }, 300)
}
onUnmounted(() => {
  clearTimeout(timer.value)
})
onMounted(() => {
  init()
})
</script>
