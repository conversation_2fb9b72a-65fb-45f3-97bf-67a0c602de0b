/**
 * 动态 API 事件管理 Composable
 * 提供类型安全、错误处理、加载状态管理的 API 调用功能
 */

import { type Ref, computed, onUnmounted, ref } from 'vue'
import {
  type ApiConfig,
  type ApiResponse,
  executeApi,
} from '@neue-plus/services'

// API 执行上下文类型
export interface ApiExecutionContext {
  [key: string]: any
  // 可以添加特定的上下文字段
  userId?: string
  tenantId?: string
  locale?: string
}

// API 执行状态
export interface ApiExecutionState {
  loading: boolean
  error: string | null
  lastExecuted: number | null
}

// API 事件函数类型
export type ApiEventFunction = (
  context?: ApiExecutionContext
) => Promise<ApiResponse>

// API 事件映射类型
export type ApiEventMap = Record<string, ApiEventFunction>

// API 状态映射类型
export type ApiStateMap = Record<string, ApiExecutionState>

/**
 * 使用动态 API 事件的 Composable
 */
export function useApiEvents(
  apiConfigs: Ref<Record<string, ApiConfig>>,
  options: {
    enableCache?: boolean
    cacheTimeout?: number
    enableRetry?: boolean
    maxRetries?: number
    onSuccess?: (apiName: string, response: ApiResponse) => void
    onError?: (apiName: string, error: Error) => void
  } = {}
) {
  const {
    enableCache = false,
    cacheTimeout = 5 * 60 * 1000, // 5分钟
    enableRetry = false,
    maxRetries = 3,
    onSuccess,
    onError,
  } = options

  // API 执行状态
  const apiStates = ref<ApiStateMap>({})

  // API 结果缓存
  const apiCache = enableCache
    ? new Map<string, { data: ApiResponse; timestamp: number }>()
    : null

  // 正在执行的请求控制器
  const abortControllers = new Map<string, AbortController>()

  /**
   * 获取 API 执行状态
   */
  const getApiState = (apiName: string): ApiExecutionState => {
    if (!apiStates.value[apiName]) {
      apiStates.value[apiName] = {
        loading: false,
        error: null,
        lastExecuted: null,
      }
    }
    return apiStates.value[apiName]
  }

  /**
   * 更新 API 状态
   */
  const updateApiState = (
    apiName: string,
    updates: Partial<ApiExecutionState>
  ) => {
    const state = getApiState(apiName)
    Object.assign(state, updates)
  }

  /**
   * 生成缓存键
   */
  const getCacheKey = (
    apiName: string,
    context?: ApiExecutionContext
  ): string => {
    return `${apiName}_${JSON.stringify(context || {})}`
  }

  /**
   * 检查缓存
   */
  const getCachedResult = (cacheKey: string): ApiResponse | null => {
    if (!apiCache) return null

    const cached = apiCache.get(cacheKey)
    if (cached && Date.now() - cached.timestamp < cacheTimeout) {
      return cached.data
    }

    // 清除过期缓存
    if (cached) {
      apiCache.delete(cacheKey)
    }

    return null
  }

  /**
   * 设置缓存
   */
  const setCachedResult = (cacheKey: string, response: ApiResponse) => {
    if (!apiCache || !response.success) return

    apiCache.set(cacheKey, {
      data: response,
      timestamp: Date.now(),
    })
  }

  /**
   * 执行 API 调用（带重试机制）
   */
  const executeApiWithRetry = async (
    apiName: string,
    config: ApiConfig,
    context?: ApiExecutionContext,
    retryCount = 0
  ): Promise<ApiResponse> => {
    try {
      // 创建 AbortController
      const controller = new AbortController()
      abortControllers.set(apiName, controller)

      // 添加 signal 到配置中
      const configWithSignal = {
        ...config,
        signal: controller.signal,
      }

      const response = await executeApi(configWithSignal, context)

      // 清除控制器
      abortControllers.delete(apiName)

      return response
    } catch (error) {
      // 清除控制器
      abortControllers.delete(apiName)

      // 如果是取消操作，直接抛出
      if (error instanceof Error && error.name === 'AbortError') {
        throw error
      }

      // 重试逻辑
      if (enableRetry && retryCount < maxRetries) {
        console.warn(
          `API ${apiName} failed, retrying... (${retryCount + 1}/${maxRetries})`
        )
        await new Promise((resolve) =>
          setTimeout(resolve, 2 ** retryCount * 1000)
        ) // 指数退避
        return executeApiWithRetry(apiName, config, context, retryCount + 1)
      }

      throw error
    }
  }

  /**
   * 创建 API 事件函数
   */
  const createApiEventFunction = (
    apiName: string,
    config: ApiConfig
  ): ApiEventFunction => {
    return async (context?: ApiExecutionContext): Promise<ApiResponse> => {
      const cacheKey = getCacheKey(apiName, context)

      // 检查缓存
      const cachedResult = getCachedResult(cacheKey)
      if (cachedResult) {
        console.log(`Using cached result for API: ${apiName}`)
        return cachedResult
      }

      // 更新加载状态
      updateApiState(apiName, {
        loading: true,
        error: null,
      })

      try {
        console.log(`Executing API: ${apiName}`, config, context)

        const response = await executeApiWithRetry(apiName, config, context)

        // 更新成功状态
        updateApiState(apiName, {
          loading: false,
          error: null,
          lastExecuted: Date.now(),
        })

        // 设置缓存
        setCachedResult(cacheKey, response)

        // 调用成功回调
        if (response.success && onSuccess) {
          onSuccess(apiName, response)
        }

        return response
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Unknown error'

        // 更新错误状态
        updateApiState(apiName, {
          loading: false,
          error: errorMessage,
        })

        // 调用错误回调
        if (onError) {
          onError(
            apiName,
            error instanceof Error ? error : new Error(errorMessage)
          )
        }

        // 返回错误响应
        return {
          data: null,
          success: false,
          error: errorMessage,
        }
      }
    }
  }

  /**
   * 动态 API 事件映射
   */
  const dynamicApiEvents = computed<ApiEventMap>(() => {
    const result: ApiEventMap = {}
    const configs = apiConfigs.value

    Object.keys(configs).forEach((apiName) => {
      result[apiName] = createApiEventFunction(apiName, configs[apiName])
    })

    return result
  })

  /**
   * 取消指定 API 的执行
   */
  const cancelApi = (apiName: string) => {
    const controller = abortControllers.get(apiName)
    if (controller) {
      controller.abort()
      abortControllers.delete(apiName)
      updateApiState(apiName, { loading: false })
    }
  }

  /**
   * 取消所有正在执行的 API
   */
  const cancelAllApis = () => {
    abortControllers.forEach((controller, apiName) => {
      controller.abort()
      updateApiState(apiName, { loading: false })
    })
    abortControllers.clear()
  }

  /**
   * 清除缓存
   */
  const clearCache = (apiName?: string) => {
    if (!apiCache) return

    if (apiName) {
      // 清除特定 API 的缓存
      const keysToDelete = Array.from(apiCache.keys()).filter((key) =>
        key.startsWith(`${apiName}_`)
      )
      keysToDelete.forEach((key) => apiCache.delete(key))
    } else {
      // 清除所有缓存
      apiCache.clear()
    }
  }

  /**
   * 重置 API 状态
   */
  const resetApiState = (apiName?: string) => {
    if (apiName) {
      delete apiStates.value[apiName]
    } else {
      apiStates.value = {}
    }
  }

  // 组件卸载时清理
  onUnmounted(() => {
    cancelAllApis()
    clearCache()
  })

  return {
    // 主要功能
    dynamicApiEvents,
    apiStates: computed(() => apiStates.value),

    // 控制方法
    cancelApi,
    cancelAllApis,
    clearCache,
    resetApiState,

    // 状态查询
    getApiState,
    isApiLoading: (apiName: string) => getApiState(apiName).loading,
    getApiError: (apiName: string) => getApiState(apiName).error,
  }
}
