import { Ref, onBeforeUnmount, onMounted, ref } from 'vue'

export function useResizeObserver<T extends HTMLElement>(
  elementRef: Ref<T | null>
) {
  const width = ref(0)
  const updateWidth = () => {
    if (elementRef.value) {
      width.value = elementRef.value.getBoundingClientRect().width
    }
  }
  onMounted(() => {
    updateWidth()
    const resizeObserver = new ResizeObserver(updateWidth)
    if (elementRef.value) {
      resizeObserver.observe(elementRef.value)
    }
    onBeforeUnmount(() => {
      if (elementRef.value) {
        resizeObserver.unobserve(elementRef.value)
      }
    })
  })
  return {
    width,
  }
}
