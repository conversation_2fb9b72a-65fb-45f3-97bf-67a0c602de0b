import { buildProps } from '@neue-plus/utils'
import type { TableColumnCtx, TableProps } from 'element-plus'
import type { ExtractPropTypes } from 'vue'
import type { NeTableColumnProps } from './table-column/type'

export type NeTableColumn = Partial<TableColumnCtx<any>> & NeTableColumnProps

export const neTableProps = buildProps({
  bordered: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Array as () => Array<any>,
    default: () => [],
  },
  columns: {
    type: Array as () => Array<NeTableColumn>,
    default: () => [],
  },
  treeProps: {
    type: Object,
    default: () => ({
      children: 'children',
      label: 'label',
      hasChildren: 'hasChildren',
    }),
  },
  rowKey: {
    type: String,
    default: 'id',
  },
} as const)

export type NeTableProps = ExtractPropTypes<typeof neTableProps> &
  TableProps<any>
