/**
 * 低代码平台 API 相关类型定义
 */

import type { ApiConfig, ODataFilterRule } from '@neue-plus/services'

// 常用的 API 配置模板
export interface ApiConfigTemplates {
  // 用户相关 API
  getUserList: ApiConfig
  getUserDetail: ApiConfig
  createUser: ApiConfig
  updateUser: ApiConfig
  deleteUser: ApiConfig

  // 表单相关 API
  submitForm: ApiConfig
  validateForm: ApiConfig

  // 文件相关 API
  uploadFile: ApiConfig
  downloadFile: ApiConfig

  // 数据查询 API
  queryData: ApiConfig
  exportData: ApiConfig
}

// 扩展的 API 执行上下文
export interface LowCodeApiContext {
  // 用户信息
  userId?: string
  userRole?: string
  tenantId?: string

  // 分页信息
  pagination?: {
    page: number
    pageSize: number
  }

  // 过滤条件
  filter?: ODataFilterRule[]

  // 排序信息
  sorting?: {
    field: string
    direction: 'asc' | 'desc'
  }[]

  // 表单数据
  formData?: Record<string, any>

  // 文件信息
  files?: File[]

  // 其他自定义参数
  [key: string]: any
}

// API 配置工厂函数
export class ApiConfigFactory {
  /**
   * 创建 REST API 配置
   */
  static createRestApi(
    url: string,
    method: 'get' | 'post' | 'put' | 'patch' | 'delete' = 'get',
    options: Partial<ApiConfig> = {}
  ): ApiConfig {
    return {
      url,
      method,
      protocol: 'rest',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    }
  }

  /**
   * 创建 OData API 配置
   */
  static createODataApi(
    url: string,
    options: {
      select?: string[]
      filter?: ODataFilterRule[]
      orderBy?: string
      top?: number
      skip?: number
      expand?: string[]
    } = {}
  ): ApiConfig {
    const { select, filter, orderBy, top, skip, expand } = options

    return {
      url,
      method: 'get',
      protocol: 'odata',
      query: {
        select: select?.join(','),
        filter,
        orderby: orderBy,
        top,
        skip,
        expand: expand?.join(','),
      },
    }
  }

  /**
   * 创建文件上传 API 配置
   */
  static createFileUploadApi(url: string): ApiConfig {
    return {
      url,
      method: 'post',
      protocol: 'rest',
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }
  }

  /**
   * 创建表单提交 API 配置
   */
  static createFormSubmitApi(
    url: string,
    method: 'post' | 'put' = 'post'
  ): ApiConfig {
    return {
      url,
      method,
      protocol: 'rest',
      headers: {
        'Content-Type': 'application/json',
      },
    }
  }
}

// 预定义的 API 配置模板
export const defaultApiTemplates: Partial<ApiConfigTemplates> = {
  // 用户管理
  getUserList: ApiConfigFactory.createODataApi('/api/users', {
    select: ['id', 'name', 'email', 'role', 'status'],
    top: 20,
  }),

  getUserDetail: ApiConfigFactory.createRestApi('/api/users/{id}'),

  createUser: ApiConfigFactory.createFormSubmitApi('/api/users', 'post'),

  updateUser: ApiConfigFactory.createFormSubmitApi('/api/users/{id}', 'put'),

  deleteUser: ApiConfigFactory.createRestApi('/api/users/{id}', 'delete'),

  // 表单处理
  submitForm: ApiConfigFactory.createFormSubmitApi('/api/forms/submit'),

  validateForm: ApiConfigFactory.createRestApi('/api/forms/validate', 'post'),

  // 文件处理
  uploadFile: ApiConfigFactory.createFileUploadApi('/api/files/upload'),

  downloadFile: ApiConfigFactory.createRestApi('/api/files/{id}/download'),

  // 数据查询
  queryData: ApiConfigFactory.createODataApi('/api/data/query'),

  exportData: ApiConfigFactory.createRestApi('/api/data/export', 'post'),
}

// API 配置验证器
export class ApiConfigValidator {
  /**
   * 验证 API 配置的完整性
   */
  static validate(config: ApiConfig): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    if (!config.url) {
      errors.push('URL is required')
    }

    if (!config.method) {
      errors.push('HTTP method is required')
    }

    if (!config.protocol) {
      errors.push('Protocol is required')
    }

    if (
      config.protocol === 'odata' &&
      config.method !== 'get' &&
      !config.query
    ) {
      errors.push('OData queries require query parameters')
    }

    return {
      valid: errors.length === 0,
      errors,
    }
  }

  /**
   * 验证 API 配置集合
   */
  static validateCollection(
    configs: Record<string, ApiConfig>
  ): Record<string, string[]> {
    const results: Record<string, string[]> = {}

    Object.entries(configs).forEach(([name, config]) => {
      const validation = this.validate(config)
      if (!validation.valid) {
        results[name] = validation.errors
      }
    })

    return results
  }
}
