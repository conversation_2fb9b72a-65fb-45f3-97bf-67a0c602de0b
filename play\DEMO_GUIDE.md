# 🎮 Services 新架构演示指南

## 概述

这个演示项目展示了 `@neue-plus/services` v2.0 的所有新功能和改进。通过交互式的演示，你可以深入了解新架构的强大功能。

## 🚀 启动演示

```bash
cd play
pnpm dev
```

访问 http://localhost:5174 查看演示页面。

## 📋 演示功能

### 1. 基础功能演示

#### 🔍 基础查询演示

- **executeApi GET**: 演示基础的 GET 请求
- **OData 查询**: 展示 OData 协议的查询功能
- **表达式解析**: 演示动态参数和上下文变量
- **上下文变量**: 展示如何使用请求上下文

#### 🔄 CRUD 操作演示

- **创建 (POST)**: 演示如何创建新数据
- **读取 (GET)**: 演示数据查询
- **更新 (PUT)**: 演示完整更新操作
- **部分更新 (PATCH)**: 演示部分字段更新
- **删除 (DELETE)**: 演示数据删除

#### ⚡ 高级功能演示

- **批量请求**: 演示批量 API 调用
- **并发请求**: 演示并发控制
- **自定义服务**: 演示服务定制

### 2. 🏗️ 新架构功能演示

#### 框架初始化

```typescript
// 演示内容
;-检查框架初始化状态 - 开发模式初始化 - 框架状态获取 - 组件注册情况
```

**操作步骤**：

1. 点击"框架初始化"按钮
2. 查看返回的框架状态信息
3. 观察控制台输出的初始化日志

#### 中间件系统

```typescript
// 演示内容
;-自定义中间件注册 - 请求前处理 - 响应后处理 - 错误处理中间件
```

**操作步骤**：

1. 点击"中间件系统"按钮
2. 打开浏览器开发者工具查看控制台
3. 观察中间件的执行日志
4. 查看响应中的中间件处理信息

#### 插件系统

```typescript
// 演示内容
;-动态插件安装 - 插件中间件注册 - 插件服务注册 - 插件功能验证
```

**操作步骤**：

1. 点击"插件系统"按钮
2. 查看控制台的插件安装日志
3. 检查返回数据中的插件服务信息
4. 验证插件中间件是否生效

#### 服务容器

```typescript
// 演示内容
;-服务注册 - 服务获取 - 依赖注入 - 服务状态检查
```

**操作步骤**：

1. 点击"服务容器"按钮
2. 查看注册的自定义服务
3. 验证服务的方法调用
4. 检查服务状态

### 3. 🔧 配置标准化演示

#### 配置标准化

```typescript
// 演示内容
;-多种配置格式处理 - 配置标准化过程 - 标准化结果对比
```

**操作步骤**：

1. 点击"配置标准化"按钮
2. 查看不同格式的原始配置
3. 对比标准化后的配置
4. 验证标准化配置的执行结果

#### 向后兼容性

```typescript
// 演示内容
;-旧版配置格式支持 - 自动配置转换 - 兼容性验证
```

**操作步骤**：

1. 点击"向后兼容性"按钮
2. 查看旧版配置格式
3. 验证配置自动转换
4. 确认请求正常执行

#### 框架状态

```typescript
// 演示内容
;-完整框架状态报告 - 性能监控信息 - 组件注册情况 - 内存使用统计
```

**操作步骤**：

1. 点击"框架状态"按钮
2. 查看详细的状态报告
3. 检查性能指标
4. 了解框架组件情况

## 🎯 新架构详细演示

### 架构概览

- **依赖注入**: 基于容器的服务管理
- **插件系统**: 可扩展的功能模块
- **中间件**: 请求/响应处理管道

### 实时状态监控

- 框架初始化状态
- 已注册服务数量
- 已安装插件数量
- 活跃中间件数量

### 代码示例

提供了四个标签页的完整代码示例：

- **基础使用**: 展示向后兼容的基础用法
- **中间件**: 演示中间件的创建和使用
- **插件开发**: 展示如何开发自定义插件
- **依赖注入**: 演示服务注册和使用

### 性能对比

详细的性能指标对比表，展示新旧版本的差异。

### 迁移指南

分步骤的迁移指导，帮助从旧版本平滑升级。

## 🔍 调试技巧

### 1. 控制台日志

新架构提供了丰富的调试日志：

```typescript
// 启用详细日志
await initializeForDevelopment()
```

### 2. 中间件调试

```typescript
// 添加调试中间件
middlewareManager.use({
  name: 'debug',
  beforeRequest: (config, context) => {
    console.log('Request:', config)
    return config
  },
  afterResponse: (response, config, context) => {
    console.log('Response:', response)
    return response
  },
})
```

### 3. 状态检查

```typescript
// 检查框架状态
const status = getFrameworkStatus()
console.log('Framework Status:', status)
```

## 📊 演示数据

演示使用了以下测试数据源：

- **OData 服务**: https://services.odata.org/V4/TripPinServiceRW
- **REST API**: https://jsonplaceholder.typicode.com
- **本地模拟数据**: 用于演示特定功能

## 🛠️ 自定义演示

你可以修改演示代码来测试自己的场景：

1. **添加自定义中间件**

   ```typescript
   middlewareManager.use({
     name: 'my-middleware',
     // 你的中间件逻辑
   })
   ```

2. **创建自定义插件**

   ```typescript
   const myPlugin = createPlugin('my-plugin', (context) => {
     // 你的插件逻辑
   })
   await installPlugin(myPlugin)
   ```

3. **注册自定义服务**
   ```typescript
   registerService('my-service', () => ({
     // 你的服务实现
   }))
   ```

## 🤔 常见问题

### Q: 为什么有些功能需要刷新页面？

A: 某些演示功能会修改全局状态，刷新页面可以重置到初始状态。

### Q: 如何查看详细的执行日志？

A: 打开浏览器开发者工具的控制台标签页，所有日志都会在那里显示。

### Q: 演示数据是真实的吗？

A: 是的，演示使用了真实的公开 API 服务，但某些操作（如删除）可能不会真正生效。

### Q: 如何重置演示状态？

A: 刷新页面即可重置所有演示状态。

## 📚 相关文档

- [新架构使用指南](../packages/services/NEW_ARCHITECTURE_GUIDE.md)
- [API 文档](../packages/services/README.md)
- [架构设计文档](../packages/services/ARCHITECTURE.md)
- [迁移指南](../packages/services/MIGRATION.md)

## 🎉 开始探索

现在你可以开始探索新架构的强大功能了！建议按照以下顺序进行：

1. 先体验基础功能，了解向后兼容性
2. 尝试新架构功能，感受新特性
3. 查看代码示例，学习最佳实践
4. 阅读详细文档，深入理解架构设计

祝你使用愉快！🚀
