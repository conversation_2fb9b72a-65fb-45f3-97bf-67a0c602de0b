/**
 * HTTP 客户端
 * 提供统一的 HTTP 请求功能
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { createServiceError, handleError } from '../core/error-handler'
import type { ApiClient, BaseRequestConfig } from '../core/types'

/**
 * HTTP 客户端配置
 */
export interface HttpClientConfig {
  baseURL?: string
  timeout?: number
  headers?: Record<string, string>
  withCredentials?: boolean
}

/**
 * HTTP 客户端实现
 */
export class HttpClient implements ApiClient {
  private axiosInstance: AxiosInstance

  constructor(config: HttpClientConfig = {}) {
    this.axiosInstance = axios.create({
      timeout: 30000,
      withCredentials: false,
      ...config,
    })

    this.setupInterceptors()
  }

  /**
   * 设置请求和响应拦截器
   */
  private setupInterceptors(): void {
    // 请求拦截器
    this.axiosInstance.interceptors.request.use(
      (config) => {
        // 可以在这里添加通用的请求处理逻辑
        // 比如添加认证头、请求 ID 等
        return config
      },
      (error) => {
        return Promise.reject(createServiceError(error))
      }
    )

    // 响应拦截器
    this.axiosInstance.interceptors.response.use(
      (response) => {
        return response
      },
      (error) => {
        const serviceError = createServiceError(error)
        handleError(serviceError)
        return Promise.reject(serviceError)
      }
    )
  }

  /**
   * 发送 HTTP 请求
   * @param config 请求配置
   * @returns 响应数据
   */
  async request<T = any>(config: BaseRequestConfig): Promise<T> {
    try {
      const axiosConfig: AxiosRequestConfig = {
        url: config.url,
        method: config.method,
        headers: config.headers,
        timeout: config.timeout,
        // 根据方法类型设置数据
        ...(config.method === 'get'
          ? { params: (config as any).params }
          : { data: (config as any).body }),
      }

      const response: AxiosResponse<T> = await this.axiosInstance.request(
        axiosConfig
      )
      return response.data
    } catch (error) {
      throw createServiceError(error)
    }
  }

  /**
   * GET 请求
   * @param url 请求 URL
   * @param params 查询参数
   * @param config 额外配置
   * @returns 响应数据
   */
  async get<T = any>(
    url: string,
    params?: Record<string, any>,
    config?: Partial<BaseRequestConfig>
  ): Promise<T> {
    return this.request<T>({
      url,
      method: 'get',
      ...config,
      params,
    } as any)
  }

  /**
   * POST 请求
   * @param url 请求 URL
   * @param data 请求体数据
   * @param config 额外配置
   * @returns 响应数据
   */
  async post<T = any>(
    url: string,
    data?: any,
    config?: Partial<BaseRequestConfig>
  ): Promise<T> {
    return this.request<T>({
      url,
      method: 'post',
      ...config,
      body: data,
    } as any)
  }

  /**
   * PUT 请求
   * @param url 请求 URL
   * @param data 请求体数据
   * @param config 额外配置
   * @returns 响应数据
   */
  async put<T = any>(
    url: string,
    data?: any,
    config?: Partial<BaseRequestConfig>
  ): Promise<T> {
    return this.request<T>({
      url,
      method: 'put',
      ...config,
      body: data,
    } as any)
  }

  /**
   * PATCH 请求
   * @param url 请求 URL
   * @param data 请求体数据
   * @param config 额外配置
   * @returns 响应数据
   */
  async patch<T = any>(
    url: string,
    data?: any,
    config?: Partial<BaseRequestConfig>
  ): Promise<T> {
    return this.request<T>({
      url,
      method: 'patch',
      ...config,
      body: data,
    } as any)
  }

  /**
   * DELETE 请求
   * @param url 请求 URL
   * @param config 额外配置
   * @returns 响应数据
   */
  async delete<T = any>(
    url: string,
    config?: Partial<BaseRequestConfig>
  ): Promise<T> {
    return this.request<T>({
      url,
      method: 'delete',
      ...config,
    })
  }

  /**
   * 设置默认头部
   * @param headers 头部对象
   */
  setDefaultHeaders(headers: Record<string, string>): void {
    Object.assign(this.axiosInstance.defaults.headers.common, headers)
  }

  /**
   * 设置认证头部
   * @param token 认证令牌
   * @param type 认证类型
   */
  setAuthHeader(token: string, type: string = 'Bearer'): void {
    this.axiosInstance.defaults.headers.common[
      'Authorization'
    ] = `${type} ${token}`
  }

  /**
   * 移除认证头部
   */
  removeAuthHeader(): void {
    delete this.axiosInstance.defaults.headers.common['Authorization']
  }

  /**
   * 获取 Axios 实例（用于高级用法）
   * @returns Axios 实例
   */
  getAxiosInstance(): AxiosInstance {
    return this.axiosInstance
  }
}

// 默认 HTTP 客户端实例
let defaultHttpClient: HttpClient | null = null

/**
 * 获取默认 HTTP 客户端实例
 * @returns HTTP 客户端实例
 */
export function getDefaultHttpClient(): HttpClient {
  if (!defaultHttpClient) {
    defaultHttpClient = new HttpClient()
  }
  return defaultHttpClient
}

/**
 * 设置默认 HTTP 客户端实例
 * @param client HTTP 客户端实例
 */
export function setDefaultHttpClient(client: HttpClient): void {
  defaultHttpClient = client
}

/**
 * 创建 HTTP 客户端实例
 * @param config 客户端配置
 * @returns HTTP 客户端实例
 */
export function createHttpClient(config?: HttpClientConfig): HttpClient {
  return new HttpClient(config)
}
