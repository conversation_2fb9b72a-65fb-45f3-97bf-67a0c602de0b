import {
  NeAvatar,
  NeButton,
  NeCard,
  NeCheckboxGroup,
  NeConfigProvider,
  NeDateRange,
  NeDrawer,
  NeInput,
  NeMaterialRender,
  NePagination,
  NeProForm,
  NeProTable,
  NeRadioGroup,
  NeSelect,
  NeTable,
  NeTooltipEllipsis,
  NeTreeTable,
} from '@neue-plus/components'
import {
  WidgetBasicForm,
  WidgetBomPanel,
  WidgetBomTable,
  WidgetBusinessType,
} from '@neue-plus/widgets'
import type { Plugin } from 'vue'

export default [
  NePagination,
  NeInput,
  NeCard,
  NeSelect,
  NeTable,
  NeProTable,
  NeProForm,
  NeDateRange,
  NeButton,
  NeRadioGroup,
  NeCheckboxGroup,
  NeConfigProvider,
  NeTreeTable,
  NeDrawer,
  NeAvatar,
  NeTooltipEllipsis,
  NeMaterialRender,
  WidgetBomTable,
  WidgetBomPanel,
  WidgetBasicForm,
  WidgetBusinessType,
] as Plugin[]
