# 🔧 适配器注册问题修复

## 问题描述

用户在使用 ApiRequestButton 执行 GET 请求时遇到错误：

```
请求失败: No adapter found for protocol: odata
```

## 🔍 问题分析

### 根本原因

1. **适配器未注册**：OData 适配器没有被正确注册到 ApiService 中
2. **初始化时序问题**：`executeApi` 函数在调用时，框架可能还没有初始化
3. **容器状态问题**：适配器注册到容器中，但 ApiService 创建时容器可能为空

### 错误流程

```
ApiRequestButton → executeApi() → getDefaultApiService() → new ApiService()
                                                              ↓
                                                    尝试从容器获取适配器
                                                              ↓
                                                        容器中没有适配器
                                                              ↓
                                                      adapters Map 为空
                                                              ↓
                                                    findAdapter() 返回 null
                                                              ↓
                                                "No adapter found for protocol: odata"
```

## 🔧 修复方案

### 1. 自动初始化机制

在 `executeApi` 函数中添加自动初始化：

```typescript
export async function executeApi(
  config: ApiConfig,
  context: RequestContext = {}
): Promise<ApiResponse> {
  // 确保框架已初始化
  await autoInitialize()

  return getDefaultApiService().execute(config, context)
}
```

### 2. autoInitialize 函数逻辑

```typescript
export async function autoInitialize(): Promise<void> {
  if (!isInitialized()) {
    await quickStart()
  }
}

export function isInitialized(): boolean {
  const container = getGlobalContainer()
  return container.has('rest-adapter') && container.has('odata-adapter')
}
```

### 3. 初始化流程

```
executeApi() → autoInitialize() → isInitialized() → quickStart() → initialize()
                                       ↓                              ↓
                                检查容器中是否有适配器              注册适配器到容器
                                       ↓                              ↓
                                如果没有则调用 quickStart()      ApiService 可以获取适配器
```

## ✅ 修复效果

### 修复前

```typescript
// 用户调用
const result = await executeApi({
  url: 'https://services.odata.org/V4/TripPinServiceRW/People',
  method: 'get',
  protocol: 'odata',
})

// 错误：No adapter found for protocol: odata
```

### 修复后

```typescript
// 用户调用（相同的代码）
const result = await executeApi({
  url: 'https://services.odata.org/V4/TripPinServiceRW/People',
  method: 'get',
  protocol: 'odata',
})

// 流程：
// 1. executeApi() 调用 autoInitialize()
// 2. autoInitialize() 检查 isInitialized()
// 3. 如果未初始化，调用 quickStart()
// 4. quickStart() 调用 initialize() 注册适配器
// 5. getDefaultApiService() 创建 ApiService
// 6. ApiService 从容器中获取已注册的适配器
// 7. findAdapter() 成功找到 odata-adapter
// 8. 请求正常执行

// 结果：请求成功执行 ✅
```

## 🎯 关键改进

### 1. **自动化初始化**

- 用户无需手动调用初始化函数
- `executeApi` 自动确保框架已初始化
- 向后兼容，不破坏现有代码

### 2. **智能检查机制**

- `isInitialized()` 检查关键适配器是否已注册
- 只在需要时进行初始化，避免重复初始化
- 高效的状态检查

### 3. **容错处理**

- 即使用户忘记初始化，系统也能正常工作
- 优雅的错误处理和恢复机制
- 清晰的错误信息

## 🧪 测试验证

### 测试用例 1：首次调用

```typescript
// 框架未初始化状态
const result = await executeApi({
  url: 'https://services.odata.org/V4/TripPinServiceRW/People',
  method: 'get',
  protocol: 'odata',
  query: { select: 'FirstName,LastName', top: 5 },
})

// 预期：自动初始化 → 请求成功
expect(result.success).toBe(true)
expect(result.data).toBeDefined()
```

### 测试用例 2：重复调用

```typescript
// 框架已初始化状态
const result1 = await executeApi({
  /* config1 */
})
const result2 = await executeApi({
  /* config2 */
})

// 预期：不重复初始化，两次请求都成功
expect(result1.success).toBe(true)
expect(result2.success).toBe(true)
```

### 测试用例 3：不同协议

```typescript
// REST 请求
const restResult = await executeApi({
  url: 'https://jsonplaceholder.typicode.com/posts/1',
  method: 'get',
  protocol: 'rest',
})

// OData 请求
const odataResult = await executeApi({
  url: 'https://services.odata.org/V4/TripPinServiceRW/People',
  method: 'get',
  protocol: 'odata',
})

// 预期：两种协议都能正常工作
expect(restResult.success).toBe(true)
expect(odataResult.success).toBe(true)
```

## 🔄 后续优化

### 1. **性能优化**

- 缓存初始化状态，避免重复检查
- 延迟加载不常用的适配器
- 优化容器查找性能

### 2. **错误处理增强**

- 更详细的适配器注册失败信息
- 适配器健康检查机制
- 自动重试机制

### 3. **开发体验改进**

- 添加初始化进度提示
- 提供初始化状态查询 API
- 增强调试信息

## 📋 总结

这个修复解决了适配器注册的核心问题：

1. **✅ 自动初始化**：用户无需手动初始化框架
2. **✅ 智能检查**：只在需要时进行初始化
3. **✅ 向后兼容**：不破坏现有代码
4. **✅ 错误预防**：避免"No adapter found"错误
5. **✅ 性能友好**：避免重复初始化

现在用户可以直接使用 `executeApi` 函数，无需担心初始化问题，系统会自动处理所有必要的设置。
