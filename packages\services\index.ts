/**
 * @neue-plus/services - 简化版统一服务层框架
 *
 * 提供轻量级的 API 调用接口：
 * - 支持 REST 和 OData 协议
 * - 统一的 executeApi 函数
 * - Axios 和 OData 客户端
 * - TypeScript 类型支持
 * - 错误处理机制
 */

// 导出所有 API
export * from './src/index'

// 向后兼容的导出（保持旧的 API 可用）
export { executeApi } from './src/utils'
export { createODataClient } from './src/clients/odata-client'
export { createAxiosClient } from './src/clients/axios-client'

// 类型别名（向后兼容）
export type { ApiConfig as ApiSchema } from './src/types'
export type { Protocol } from './src/types'

// 便捷函数导出
export { executeApi as execute } from './src/utils'
export { executeApi as request } from './src/utils'

// 默认导出
export { executeApi as default } from './src/utils'
