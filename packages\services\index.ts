/**
 * @neue-plus/services - 重构后的统一服务层框架
 *
 * 新的架构提供了更清晰的模块化设计：
 * - 核心类型和接口
 * - 表达式解析器
 * - 错误处理机制
 * - HTTP 和 OData 客户端
 * - 适配器模式
 * - 统一的 API 服务
 * - 客户端工厂
 */

// 导出所有新的 API
export * from './src/index'

// 向后兼容的导出（保持旧的 API 可用）
export { executeApi } from './src/services/api-service'
export { parseExpression } from './src/core/expression-parser'
export { setErrorHandler } from './src/core/error-handler'
export { createODataClient } from './src/factories/client-factory'

// 旧的类型别名（向后兼容）
export type { LegacyApiSchema as ApiSchema } from './src/core/types'
export type { Protocol } from './src/core/types'
