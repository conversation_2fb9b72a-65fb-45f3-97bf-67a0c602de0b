# 🚀 ApiRequestButton 组件使用指南

## 概述

`ApiRequestButton` 是一个通用的 API 请求测试组件，支持 GET、POST、PUT、PATCH、DELETE 五种 HTTP 方法，可以用于快速测试和调试 API 接口。

## 🎯 功能特性

### 1. 支持的 HTTP 方法

- **GET**: 获取数据
- **POST**: 创建新数据
- **PUT**: 完整更新数据
- **PATCH**: 部分更新数据
- **DELETE**: 删除数据

### 2. 协议支持

- **REST**: 标准 REST API
- **OData**: OData 协议支持

### 3. 请求配置

- **Headers**: 自定义请求头
- **Query Params**: URL 查询参数
- **Request Body**: 请求体（POST/PUT/PATCH）
- **OData Options**: OData 查询选项

### 4. 响应处理

- **成功响应**: 格式化显示响应数据
- **错误处理**: 显示错误信息
- **性能监控**: 显示请求耗时
- **数据统计**: 显示数据总数等信息

## 🔧 使用方法

### 1. 基本使用

```vue
<template>
  <ApiRequestButton />
</template>

<script setup>
import ApiRequestButton from './components/ApiRequestButton.vue'
</script>
```

### 2. 在现有项目中集成

组件已经集成到 `play/src/components/services/index.vue` 中，可以直接使用。

## 📋 操作指南

### 1. 发送 GET 请求

1. **选择方法**: 选择 "GET"
2. **输入 URL**: 例如 `https://jsonplaceholder.typicode.com/posts`
3. **设置协议**: 选择 "REST"
4. **添加参数**（可选）:
   - 在 "Query Params" 标签页添加参数
   - 例如: `_limit` = `5`
5. **发送请求**: 点击 "发送请求" 按钮

### 2. 发送 POST 请求

1. **选择方法**: 选择 "POST"
2. **输入 URL**: 例如 `https://jsonplaceholder.typicode.com/posts`
3. **设置 Headers**:
   - `Content-Type` = `application/json`
4. **设置请求体**:
   ```json
   {
     "title": "foo",
     "body": "bar",
     "userId": 1
   }
   ```
5. **发送请求**: 点击 "发送请求" 按钮

### 3. 发送 OData 请求

1. **选择方法**: 选择 "GET"
2. **输入 URL**: 例如 `https://services.odata.org/V4/TripPinServiceRW/People`
3. **设置协议**: 选择 "OData"
4. **配置 OData 选项**:
   - `$select`: `FirstName,LastName,Gender`
   - `$filter`: `Gender eq 'Female'`
   - `$top`: `5`
5. **发送请求**: 点击 "发送请求" 按钮

### 4. 发送 PUT/PATCH 请求

1. **选择方法**: 选择 "PUT" 或 "PATCH"
2. **输入 URL**: 例如 `https://jsonplaceholder.typicode.com/posts/1`
3. **设置请求体**:
   ```json
   {
     "id": 1,
     "title": "updated title",
     "body": "updated body",
     "userId": 1
   }
   ```
4. **发送请求**: 点击 "发送请求" 按钮

### 5. 发送 DELETE 请求

1. **选择方法**: 选择 "DELETE"
2. **输入 URL**: 例如 `https://jsonplaceholder.typicode.com/posts/1`
3. **发送请求**: 点击 "发送请求" 按钮

## 🎨 界面功能

### 1. 请求配置区域

- **方法选择器**: 下拉选择 HTTP 方法
- **URL 输入框**: 输入 API 地址
- **协议选择器**: 选择 REST 或 OData

### 2. 参数配置标签页

- **Headers**: 配置请求头
- **Query Params**: 配置 URL 参数
- **Body**: 配置请求体（仅 POST/PUT/PATCH）
- **OData**: 配置 OData 查询选项（仅 OData 协议）

### 3. 操作按钮

- **发送请求**: 执行 API 请求
- **清空所有**: 重置所有配置
- **加载示例**: 加载预设的示例数据

### 4. 响应显示区域

- **响应状态**: 成功/失败状态
- **响应时间**: 请求耗时
- **数据统计**: 数据总数等信息
- **响应数据**: 格式化的 JSON 响应

## 🔍 示例场景

### 1. 测试 REST API

```
方法: GET
URL: https://jsonplaceholder.typicode.com/posts
协议: REST
参数: _limit = 10
```

### 2. 测试 OData API

```
方法: GET
URL: https://services.odata.org/V4/TripPinServiceRW/People
协议: OData
OData 选项:
  - $select: FirstName,LastName
  - $top: 5
  - $filter: Gender eq 'Female'
```

### 3. 创建数据

```
方法: POST
URL: https://jsonplaceholder.typicode.com/posts
Headers: Content-Type = application/json
Body:
{
  "title": "My New Post",
  "body": "This is the content",
  "userId": 1
}
```

### 4. 更新数据

```
方法: PUT
URL: https://jsonplaceholder.typicode.com/posts/1
Headers: Content-Type = application/json
Body:
{
  "id": 1,
  "title": "Updated Post",
  "body": "Updated content",
  "userId": 1
}
```

### 5. 部分更新

```
方法: PATCH
URL: https://jsonplaceholder.typicode.com/posts/1
Headers: Content-Type = application/json
Body:
{
  "title": "Partially Updated Title"
}
```

### 6. 删除数据

```
方法: DELETE
URL: https://jsonplaceholder.typicode.com/posts/1
```

## 🛠️ 高级功能

### 1. JSON 格式化

在 Body 标签页中，可以使用 "格式化 JSON" 按钮来格式化 JSON 数据。

### 2. 示例数据加载

点击 "加载示例" 按钮可以快速加载预设的示例数据：

- REST 协议: 加载 JSONPlaceholder 示例
- OData 协议: 加载 TripPin 服务示例

### 3. 动态参数管理

- 可以动态添加/删除 Headers 和 Query Params
- 支持空值过滤，只有非空的参数才会被发送

### 4. 错误处理

- 自动捕获网络错误
- 显示详细的错误信息
- 支持错误消息关闭

## 🎯 最佳实践

### 1. 测试流程

1. 先使用 GET 方法测试 API 连通性
2. 使用示例数据验证基本功能
3. 逐步添加自定义参数
4. 测试不同的 HTTP 方法

### 2. 调试技巧

1. 打开浏览器开发者工具查看网络请求
2. 使用 JSON 格式化功能确保数据格式正确
3. 检查响应时间来评估 API 性能
4. 利用错误信息定位问题

### 3. 安全注意事项

1. 不要在生产环境中暴露敏感的 API 密钥
2. 使用 HTTPS 协议保护数据传输
3. 验证输入数据的格式和内容

## 🔧 自定义扩展

### 1. 添加新的 HTTP 方法

可以在 `httpMethods` 数组中添加新的方法：

```typescript
const httpMethods = [
  // 现有方法...
  { label: 'HEAD', value: 'head', color: '#909399', disabled: false },
  { label: 'OPTIONS', value: 'options', color: '#909399', disabled: false },
]
```

### 2. 添加预设模板

可以扩展 `loadExample` 方法来支持更多预设模板。

### 3. 自定义样式

组件使用了 Element Plus 的样式系统，可以通过 CSS 变量或覆盖样式来自定义外观。

## 📚 相关文档

- [Services 新架构指南](../../../packages/services/NEW_ARCHITECTURE_GUIDE.md)
- [Element Plus 文档](https://element-plus.org/)
- [HTTP 方法规范](https://developer.mozilla.org/en-US/docs/Web/HTTP/Methods)
- [OData 协议文档](https://www.odata.org/)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个组件！
