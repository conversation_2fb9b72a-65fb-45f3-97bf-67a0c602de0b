# 迁移指南 - Services 重构

## 概述

我们对 `@neue-plus/services` 进行了全面的重构，提供了更清晰的架构和更强大的功能。本指南将帮助您从旧版本迁移到新版本。

## 新架构概览

### 目录结构

```
src/
├── core/                    # 核心模块
│   ├── types.ts            # 类型定义
│   ├── expression-parser.ts # 表达式解析器
│   └── error-handler.ts    # 错误处理
├── clients/                 # 客户端实现
│   ├── http-client.ts      # HTTP 客户端
│   └── odata-client.ts     # OData 客户端
├── adapters/               # 适配器模式
│   ├── rest-adapter.ts     # REST 适配器
│   └── odata-adapter.ts    # OData 适配器
├── services/               # 服务层
│   └── api-service.ts      # 统一 API 服务
├── factories/              # 工厂模式
│   └── client-factory.ts   # 客户端工厂
└── index.ts                # 主入口
```

### 设计原则

1. **单一职责**: 每个模块只负责一个特定功能
2. **依赖注入**: 支持自定义实现和配置
3. **适配器模式**: 支持多种协议和扩展
4. **工厂模式**: 简化客户端创建和管理
5. **向后兼容**: 保持旧 API 的可用性

## 迁移步骤

### 1. 基本用法（无需修改）

旧的 `executeApi` 函数仍然可用，无需修改现有代码：

```typescript
// ✅ 这些代码无需修改
import { executeApi } from '@neue-plus/services'

const result = await executeApi({
  url: '/api/users',
  method: 'get',
  protocol: 'odata',
  odata: {
    select: 'name,email',
    top: 10,
  },
})
```

### 2. 类型导入更新

```typescript
// ❌ 旧的导入方式
import type { ApiSchema, Protocol } from '@neue-plus/services'

// ✅ 新的导入方式（推荐）
import type {
  ApiConfig, // 新的统一配置类型
  LegacyApiSchema, // 旧的配置类型（向后兼容）
  Protocol,
  ODataQueryOptions,
  ApiResponse,
} from '@neue-plus/services'

// ✅ 向后兼容的导入方式
import type { ApiSchema, Protocol } from '@neue-plus/services'
```

### 3. OData 客户端使用

```typescript
// ❌ 旧的方式
import { createODataClient } from '@neue-plus/services'

// ✅ 新的方式（推荐）
import { createODataClient } from '@neue-plus/services'

const client = createODataClient({
  baseUrl: 'https://api.example.com/odata',
  timeout: 30000,
  auth: {
    type: 'bearer',
    credentials: { token: 'your-token' },
  },
})

const result = await client.query('Users', {
  select: 'name,email',
  filter: 'active eq true',
  top: 10,
  count: true,
})
```

### 4. 错误处理更新

```typescript
// ❌ 旧的方式
import { setErrorHandler } from '@neue-plus/services'

setErrorHandler((message) => {
  console.error(message)
})

// ✅ 新的方式（更强大）
import {
  setErrorHandler,
  ServiceError,
  NetworkError,
  handleError,
} from '@neue-plus/services'

setErrorHandler((message) => {
  console.error('[Services]:', message)
})

// 使用新的错误类型
try {
  await executeApi(config)
} catch (error) {
  if (error instanceof NetworkError) {
    console.log('网络错误:', error.message)
  } else if (error instanceof ServiceError) {
    console.log('服务错误:', error.code, error.message)
  }
}
```

### 5. 表达式解析更新

```typescript
// ❌ 旧的方式
import { parseExpression } from '@neue-plus/services'

// ✅ 新的方式（功能相同，但更灵活）
import {
  parseExpression,
  setExpressionParser,
  createCustomParser,
} from '@neue-plus/services'

// 自定义表达式解析器
const customParser = createCustomParser((expression, context) => {
  // 自定义解析逻辑
  return context[expression] || expression
})

setExpressionParser(customParser)
```

## 新功能

### 1. 统一的 API 服务

```typescript
import { ApiService, createApiService } from '@neue-plus/services'

// 创建自定义服务实例
const apiService = createApiService()

// 注册自定义适配器
apiService.registerAdapter(myCustomAdapter)

// 执行请求
const result = await apiService.execute(config, context)
```

### 2. 客户端工厂

```typescript
import {
  ClientFactory,
  createClientFactory,
  getCacheStats,
} from '@neue-plus/services'

// 创建工厂实例
const factory = createClientFactory()

// 设置默认配置
factory.setDefaultODataConfig({
  timeout: 30000,
  headers: { 'Custom-Header': 'value' },
})

// 批量创建客户端
const clients = factory.createODataClients([
  { baseUrl: 'https://api1.example.com/odata' },
  { baseUrl: 'https://api2.example.com/odata' },
])

// 查看缓存统计
console.log(getCacheStats())
```

### 3. 批量和并发请求

```typescript
import { executeBatchApi, executeConcurrentApi } from '@neue-plus/services'

// 批量执行（顺序）
const results = await executeBatchApi([
  { url: '/api/users', method: 'get' },
  { url: '/api/posts', method: 'get' },
])

// 并发执行（限制并发数）
const results = await executeConcurrentApi(
  [
    { url: '/api/users/1', method: 'get' },
    { url: '/api/users/2', method: 'get' },
    { url: '/api/users/3', method: 'get' },
  ],
  {},
  2
) // 最多同时 2 个请求
```

### 4. 高级错误处理

```typescript
import { withRetry } from '@neue-plus/services'

class MyService {
  @withRetry(3, 1000) // 重试 3 次，延迟 1 秒
  async fetchData() {
    return await executeApi(config)
  }
}
```

## 性能优化

### 1. 客户端缓存

新架构自动缓存客户端实例，相同配置的客户端会被复用：

```typescript
// 这两个调用会使用同一个客户端实例
const client1 = createODataClient({ baseUrl: 'https://api.example.com' })
const client2 = createODataClient({ baseUrl: 'https://api.example.com' })

console.log(client1 === client2) // false（不同实例）
// 但内部的 HTTP 客户端会被复用
```

### 2. 缓存管理

```typescript
import {
  clearAllClientCache,
  clearODataClientCache,
  getCacheStats,
} from '@neue-plus/services'

// 查看缓存状态
console.log(getCacheStats())

// 清除缓存
clearODataClientCache()
clearAllClientCache()
```

## 破坏性变更

### 1. 移除的功能

- `buildRestParams` 函数已移除（功能被 `parseExpression` 覆盖）
- 一些内部类型定义已重命名

### 2. 行为变更

- 错误处理更加严格，会抛出具体的错误类型
- OData 客户端的响应格式更加标准化
- 表达式解析支持更复杂的嵌套属性访问

## 最佳实践

### 1. 使用新的类型定义

```typescript
// ✅ 推荐
import type { ApiConfig, ApiResponse } from '@neue-plus/services'

const config: ApiConfig = {
  url: '/api/users',
  method: 'get',
  protocol: 'odata',
  query: { select: 'name,email' },
}
```

### 2. 利用客户端缓存

```typescript
// ✅ 推荐：使用工厂方法，自动缓存
import { getOrCreateODataClient } from '@neue-plus/services'

const client = getOrCreateODataClient({
  baseUrl: 'https://api.example.com/odata',
})
```

### 3. 统一错误处理

```typescript
// ✅ 推荐：设置全局错误处理器
import { setErrorHandler } from '@neue-plus/services'

setErrorHandler((message) => {
  // 集成到你的日志系统
  logger.error('[API Error]:', message)

  // 显示用户友好的错误消息
  showNotification('操作失败，请稍后重试')
})
```

## 总结

新的架构提供了：

- ✅ **更好的类型安全**
- ✅ **更清晰的模块化设计**
- ✅ **更强大的错误处理**
- ✅ **更灵活的扩展性**
- ✅ **更好的性能**
- ✅ **完全的向后兼容**

大多数现有代码无需修改即可继续工作，但我们建议逐步迁移到新的 API 以获得更好的开发体验。
