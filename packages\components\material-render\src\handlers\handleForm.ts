export async function handleForm(action: any) {
  const { formRef, method = 'validate' } = action
  if (!formRef?.value) return

  const form = formRef.value

  if (method === 'reset') {
    form.resetFields()
  } else if (method === 'validate') {
    try {
      await form.validate()
      action.onSuccess?.()
    } catch (err) {
      action.onFail?.(err)
    }
  } else if (method === 'submit') {
    form.validate().then((valid: boolean) => {
      if (valid) action.onSuccess?.(form.model)
      else action.onFail?.()
    })
  }
}
