/**
 * 配置标准化器
 * 负责将不同格式的配置标准化为统一格式
 */

import type { ConfigNormalizer } from './interfaces'
import type {
  ApiConfig,
  LegacyApiSchema,
  ODataApiConfig,
  RestApiConfig,
} from './types'

/**
 * 旧版配置标准化器
 * 将旧版的 LegacyApiSchema 转换为新的配置格式
 */
export class LegacyConfigNormalizer
  implements ConfigNormalizer<LegacyApiSchema, ApiConfig>
{
  /**
   * 检查是否可以处理该配置
   */
  canHandle(config: any): config is LegacyApiSchema {
    return (
      typeof config === 'object' &&
      config !== null &&
      typeof config.url === 'string' &&
      typeof config.method === 'string' &&
      // 检查是否包含旧版特有的字段
      ('odata' in config || 'map' in config || !('query' in config))
    )
  }

  /**
   * 标准化配置
   */
  normalize(config: LegacyApiSchema): ApiConfig {
    if (config.protocol === 'odata') {
      // 转换为新的 OData 配置格式
      return {
        url: config.url,
        method: config.method,
        protocol: 'odata',
        headers: config.headers,
        params: config.params,
        body: config.body,
        dataPath: config.dataPath,
        query: config.odata || {},
        timeout: config.timeout,
      } as ODataApiConfig
    } else {
      // 转换为新的 REST 配置格式
      return {
        url: config.url,
        method: config.method,
        protocol: 'rest',
        headers: config.headers,
        params: config.params,
        body: config.body,
        dataPath: config.dataPath,
        timeout: config.timeout,
      } as RestApiConfig
    }
  }
}

/**
 * REST 配置标准化器
 * 确保 REST 配置的完整性
 */
export class RestConfigNormalizer
  implements ConfigNormalizer<RestApiConfig, RestApiConfig>
{
  /**
   * 检查是否可以处理该配置
   */
  canHandle(config: any): config is RestApiConfig {
    return (
      typeof config === 'object' &&
      config !== null &&
      typeof config.url === 'string' &&
      typeof config.method === 'string' &&
      (!config.protocol || config.protocol === 'rest')
    )
  }

  /**
   * 标准化配置
   */
  normalize(config: RestApiConfig): RestApiConfig {
    return {
      ...config,
      protocol: 'rest', // 确保协议字段存在
      headers: config.headers || {},
      params: config.params || {},
      timeout: config.timeout || 30000,
    }
  }
}

/**
 * OData 配置标准化器
 * 确保 OData 配置的完整性
 */
export class ODataConfigNormalizer
  implements ConfigNormalizer<ODataApiConfig, ODataApiConfig>
{
  /**
   * 检查是否可以处理该配置
   */
  canHandle(config: any): config is ODataApiConfig {
    return (
      typeof config === 'object' &&
      config !== null &&
      typeof config.url === 'string' &&
      typeof config.method === 'string' &&
      config.protocol === 'odata'
    )
  }

  /**
   * 标准化配置
   */
  normalize(config: ODataApiConfig): ODataApiConfig {
    return {
      ...config,
      headers: config.headers || {},
      params: config.params || {},
      query: config.query || {},
      timeout: config.timeout || 30000,
    }
  }
}

/**
 * 复合配置标准化器
 * 组合多个标准化器，按优先级处理
 */
export class CompositeConfigNormalizer implements ConfigNormalizer {
  private normalizers: ConfigNormalizer[] = []

  constructor(normalizers: ConfigNormalizer[] = []) {
    this.normalizers = [...normalizers]
  }

  /**
   * 添加标准化器
   */
  addNormalizer(normalizer: ConfigNormalizer): void {
    this.normalizers.push(normalizer)
  }

  /**
   * 移除标准化器
   */
  removeNormalizer(normalizer: ConfigNormalizer): boolean {
    const index = this.normalizers.indexOf(normalizer)
    if (index >= 0) {
      this.normalizers.splice(index, 1)
      return true
    }
    return false
  }

  /**
   * 检查是否可以处理该配置
   */
  canHandle(config: any): boolean {
    return this.normalizers.some((normalizer) => normalizer.canHandle(config))
  }

  /**
   * 标准化配置
   */
  normalize(config: any): any {
    for (const normalizer of this.normalizers) {
      if (normalizer.canHandle(config)) {
        return normalizer.normalize(config)
      }
    }

    // 如果没有合适的标准化器，返回原配置
    return config
  }

  /**
   * 获取所有标准化器
   */
  getNormalizers(): ConfigNormalizer[] {
    return [...this.normalizers]
  }

  /**
   * 清除所有标准化器
   */
  clear(): void {
    this.normalizers = []
  }
}

/**
 * 创建默认的配置标准化器
 */
export function createDefaultNormalizer(): ConfigNormalizer {
  return new CompositeConfigNormalizer([
    new LegacyConfigNormalizer(),
    new ODataConfigNormalizer(),
    new RestConfigNormalizer(),
  ])
}

// 全局标准化器实例
let globalNormalizer: ConfigNormalizer | null = null

/**
 * 获取全局配置标准化器
 */
export function getGlobalNormalizer(): ConfigNormalizer {
  if (!globalNormalizer) {
    globalNormalizer = createDefaultNormalizer()
  }
  return globalNormalizer
}

/**
 * 设置全局配置标准化器
 */
export function setGlobalNormalizer(normalizer: ConfigNormalizer): void {
  globalNormalizer = normalizer
}

/**
 * 标准化配置的便捷函数
 */
export function normalizeConfig(config: any): any {
  const normalizer = getGlobalNormalizer()
  return normalizer.normalize(config)
}

/**
 * 检查配置是否可以被标准化
 */
export function canNormalizeConfig(config: any): boolean {
  const normalizer = getGlobalNormalizer()
  return normalizer.canHandle(config)
}
