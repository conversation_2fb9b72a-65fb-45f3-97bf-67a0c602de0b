import { defineComponent } from 'vue'
import { ElDatePicker } from 'element-plus'
import { neDateRangeProps } from './types'
import { shortcuts } from './constant'

const NeDateRange = defineComponent({
  name: 'NeDateRange',
  props: neDateRangeProps,
  setup(props) {
    const { ...restProps } = props
    return () => (
      <ElDatePicker
        {...restProps}
        type={'datetimerange'}
        shortcuts={shortcuts}
      ></ElDatePicker>
    )
  },
})

export default NeDateRange
