# OData 客户端实现 - 基于 @odata/client

## 概述

我们已经成功使用 `@odata/client` 框架重新实现了 OData 客户端，提供了完整的 OData v4 协议支持。

## 技术栈

- **@odata/client**: 核心 OData 客户端库
- **TypeScript**: 完整的类型安全支持
- **Axios**: 底层 HTTP 请求（由 @odata/client 内部使用）

## 实现特点

### 1. 基于 @odata/client 的封装

```typescript
import { OData } from '@odata/client'

export class ODataClient implements IODataClient {
  private odataClient: any
  private config: ODataClientConfig

  constructor(config: ODataClientConfig) {
    this.config = config

    // 创建 OData 客户端实例
    this.odataClient = OData.New4({
      serviceEndpoint: config.baseUrl,
    })
  }
}
```

### 2. 统一的 newRequest API

所有操作都使用 `@odata/client` 的 `newRequest` API，确保一致性：

```typescript
// 查询实体集合
const response = await this.odataClient.newRequest({
  collection: targetEntitySet,
  params: params,
})

// 获取单个实体
const response = await this.odataClient.newRequest({
  collection: `${targetEntitySet}(${keyValue})`,
  params,
})

// 创建实体
const response = await this.odataClient.newRequest({
  collection: targetEntitySet,
  method: 'POST',
  data: entity,
})
```

### 3. 完整的 CRUD 支持

#### 查询 (GET)

- ✅ 实体集合查询
- ✅ 单个实体获取
- ✅ 完整的 OData 查询选项支持
- ✅ 分页信息处理

#### 创建 (POST)

- ✅ 新实体创建
- ✅ 自动处理响应数据

#### 更新 (PUT/PATCH)

- ✅ 完整更新 (PUT)
- ✅ 部分更新 (PATCH)
- ✅ 键值自动处理

#### 删除 (DELETE)

- ✅ 实体删除
- ✅ 错误处理

### 4. OData 查询选项支持

```typescript
interface ODataQueryOptions {
  filter?: string // $filter
  select?: string // $select
  expand?: string // $expand
  orderby?: string // $orderby
  top?: number // $top
  skip?: number // $skip
  count?: boolean // $count
  search?: string // $search
  apply?: string // $apply
  custom?: Record<string, any>
}
```

### 5. 认证支持

```typescript
// Basic 认证
case 'basic':
  const credentials = btoa(`${username}:${password}`)
  this.odataClient.setHeader('Authorization', `Basic ${credentials}`)

// Bearer Token 认证
case 'bearer':
case 'oauth2':
  this.odataClient.setHeader('Authorization', `Bearer ${token}`)
```

### 6. 高级功能

#### 函数调用

```typescript
async callFunction<T = any>(
  functionName: string,
  parameters: Record<string, any> = {},
  context: RequestContext = {}
): Promise<T | null>
```

#### 操作调用

```typescript
async callAction<T = any>(
  actionName: string,
  parameters: Record<string, any> = {},
  context: RequestContext = {}
): Promise<T | null>
```

#### 元数据获取

```typescript
async getMetadata(): Promise<string | null>
```

## API 使用示例

### 基础查询

```typescript
const client = createODataClient({
  baseUrl: 'https://services.odata.org/V4/TripPinServiceRW',
})

// 查询实体集合
const result = await client.query('People', {
  select: 'FirstName,LastName,Gender',
  filter: 'Gender eq "Male"',
  orderby: 'FirstName asc',
  top: 10,
  count: true,
})
```

### CRUD 操作

```typescript
// 创建
const newPerson = await client.create(
  {
    FirstName: 'John',
    LastName: 'Doe',
    Gender: 'Male',
  },
  'People'
)

// 获取
const person = await client.get('johndoe', 'People', {
  select: 'FirstName,LastName,Gender',
})

// 更新
const updated = await client.update(
  'johndoe',
  {
    FirstName: 'Johnny',
    LastName: 'Doe',
  },
  'People'
)

// 部分更新
const patched = await client.patch(
  'johndoe',
  {
    FirstName: 'Jonathan',
  },
  'People'
)

// 删除
const deleted = await client.delete('johndoe', 'People')
```

## 与旧实现的对比

| 特性               | 旧实现                  | 新实现 (@odata/client)          |
| ------------------ | ----------------------- | ------------------------------- |
| **基础架构**       | 自定义 HTTP 封装        | 基于成熟的 @odata/client        |
| **OData 协议支持** | 部分支持                | 完整的 OData v4 支持            |
| **类型安全**       | 基础                    | 完整的 TypeScript 支持          |
| **错误处理**       | 基础                    | 统一的错误处理机制              |
| **认证支持**       | 有限                    | 完整的认证方式支持              |
| **查询构建**       | 手动拼接                | 使用 @odata/client 的参数构建器 |
| **维护性**         | 需要手动维护 OData 逻辑 | 依赖成熟库，维护成本低          |

## 优势

### 1. 成熟稳定

- 基于经过验证的 `@odata/client` 库
- 完整的 OData v4 协议支持
- 活跃的社区维护

### 2. 功能完整

- 支持所有标准 OData 操作
- 完整的查询选项支持
- 函数和操作调用支持

### 3. 易于维护

- 减少自定义 OData 逻辑
- 统一的 API 接口
- 良好的错误处理

### 4. 向后兼容

- 保持相同的接口定义
- 无需修改现有调用代码
- 渐进式升级

## 测试验证

### 1. 构建测试

- ✅ TypeScript 编译通过
- ✅ 类型检查通过
- ✅ 模块导出正确

### 2. 功能测试

- ✅ play 项目启动成功
- ✅ 所有演示功能可用
- ✅ CRUD 操作完整支持

### 3. 集成测试

- ✅ 与现有架构兼容
- ✅ 适配器模式正常工作
- ✅ 错误处理机制有效

## 部署建议

### 1. 渐进式迁移

- 保持旧 API 的向后兼容
- 逐步迁移到新的实现
- 充分测试关键功能

### 2. 性能监控

- 监控请求响应时间
- 检查内存使用情况
- 验证缓存机制效果

### 3. 错误监控

- 设置错误日志收集
- 监控 OData 服务可用性
- 建立告警机制

## 总结

通过使用 `@odata/client` 重新实现 OData 客户端，我们获得了：

- 🚀 **更强大的功能**: 完整的 OData v4 协议支持
- 🛡️ **更好的稳定性**: 基于成熟的开源库
- 🔧 **更易维护**: 减少自定义逻辑，降低维护成本
- 📈 **更好的性能**: 优化的请求处理和缓存机制
- 🔒 **更安全的类型**: 完整的 TypeScript 支持

这个实现为 `@neue-plus/services` 提供了坚实的基础，支持未来的扩展和演进。
