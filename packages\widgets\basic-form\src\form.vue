<template>
  <widget-wrapper>
    <el-form ref="formRef" :model="formModel" label-width="100px">
      <el-form-item
        v-for="item in newFormItems"
        :key="item.prop"
        :prop="item.prop"
        :label="item.fieldName"
      >
        <!-- input -->
        <el-input
          v-if="item.fieldType === 'text'"
          v-model="formModel[item.prop]"
          :placeholder="item.placeholder"
          :type="item.inputType || 'text'"
          :disabled="item.disabled || false"
          :minlength="item.minLength || 0"
          :maxlength="item.maxLength || 255"
          :show-word-limit="item.showWordLimit || true"
          style="width: 100%"
        />
        <!-- textarea -->
        <el-input
          v-else-if="item.fieldType === 'textarea'"
          v-model="formModel[item.prop]"
          :placeholder="item.placeholder"
          :disabled="item.disabled || false"
          type="textarea"
          :rows="item.rows || 4"
          :minlength="item.minLength || 0"
          :maxlength="item.maxLength || 255"
          :show-word-limit="item.showWordLimit || true"
          style="width: 100%"
        />
        <!-- select -->
        <el-select
          v-if="item.fieldType === 'select'"
          v-model="formModel[item.prop]"
          :placeholder="item.placeholder"
          :disabled="item.disabled || false"
          style="width: 100%"
        >
          <el-option
            v-for="option in item.options"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
        <el-input-number
          v-else-if="item.fieldType === 'number'"
          v-model="formModel[item.prop]"
          :placeholder="item.placeholder"
          :min="item.min || 0"
          :max="item.max || 100"
          :step="item.step || 1"
          :disabled="item.disabled || false"
          style="width: 100%"
        />
        <!-- switch -->
        <el-switch
          v-else-if="item.fieldType === 'switch'"
          v-model="formModel[item.prop]"
          :active-text="item.activeText || '开'"
          :inactive-text="item.inactiveText || '关'"
          :disabled="item.disabled || false"
        />
        <!-- checkbox -->
        <el-checkbox-group
          v-else-if="item.fieldType === 'checkbox'"
          v-model="formModel[item.prop]"
          :disabled="item.disabled || false"
          style="width: 100%"
        >
          <el-checkbox
            v-for="option in item.options"
            :key="option.value"
            :value="option.value"
            :label="option.label"
          />
        </el-checkbox-group>
        <!-- radio -->
        <el-radio-group
          v-else-if="item.fieldType === 'radio'"
          v-model="formModel[item.prop]"
          :disabled="item.disabled || false"
          style="width: 100%"
        >
          <el-radio
            v-for="option in item.options"
            :key="option.value"
            :value="option.value"
            :label="option.label"
          />
        </el-radio-group>
        <!-- date picker -->
        <el-date-picker
          v-else-if="item.fieldType === 'date'"
          v-model="formModel[item.prop]"
          :type="item.dateType || 'date'"
          :placeholder="item.placeholder"
          :format="item.format || 'yyyy-MM-dd'"
          :value-format="item.valueFormat || 'yyyy-MM-dd'"
          style="width: 100%"
        />
        <!-- time picker -->
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm">{{ '提交' }}</el-button>
        <el-button @click="resetForm">{{ '重置' }}</el-button>
        <el-button @click="cancelForm">{{ '取消' }}</el-button>
      </el-form-item>
    </el-form>
  </widget-wrapper>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import WidgetWrapper from '@neue-plus/components/widget-wrapper'
import type { PropType } from 'vue'
import type { FieldType, FormConfig } from './form'

defineOptions({
  name: 'BasicForm',
  inheritAttrs: false,
})

const props = defineProps({
  config: {
    type: Object as PropType<FormConfig>,
    default: () => ({}),
  },
  formItems: {
    type: Array as PropType<any>,
    default: () => [],
  },
})

const formComponents = ref(['text', 'select', 'number', 'textarea', 'date'])
// const formComponents = ref(['text', 'select', 'number', 'switch', 'checkbox', 'radio', 'textarea', 'date'])

const emit = defineEmits(['formSubmit', 'formReset', 'formCancel'])

const formConfig = ref<FormConfig>({
  layout: 'horizontal',
  submitText: '提交',
  resetText: '重置',
  cancelText: '取消',
  showSubmit: true,
  showReset: false,
  showCancel: true,
  buttonAlign: 'center',
})
const formItemsData = ref<FieldType[]>([])
const formRef = ref()

const formModel = ref<any>({
  name: '1',
})

const submitForm = () => {
  // formRef.value.validate((valid: boolean) => {
  //   if (valid) {
  //     console.log('提交成功:', formData.value)
  //   } else {
  //     console.error('表单验证失败')
  //     return false
  //   }
  // })
  emit('formSubmit', {
    res: true,
    formData: formModel.value,
  })
  console.log('表单已提交:', formModel.value)
}

const resetForm = () => {
  formRef.value.resetFields()
  emit('formReset')
}

const cancelForm = () => {
  formRef.value.resetFields()
  console.log('表单已取消')
  emit('formCancel')
}

const newFormItems = computed(() => {
  return props.formItems.filter((i: { fieldType: string }) =>
    formComponents.value.includes(i.fieldType)
  )
})
watch(
  () => props.config,
  (newConfig: FormConfig) => {
    console.log('配置已更新:', newConfig)
    // 可以在这里处理配置更新逻辑
    formConfig.value = { ...formConfig.value, ...newConfig }
  },
  { deep: true }
)

watch(
  () => props.formItems,
  (newItems) => {
    console.log('表单项已更新:', newItems)
    // 可以在这里处理表单项更新逻辑
    formItemsData.value = [...newItems]
  },
  { deep: true }
)

// defineExpose({

// })
</script>

<style scoped />
