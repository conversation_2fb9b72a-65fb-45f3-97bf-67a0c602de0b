/**
 * OData 过滤器构建工具
 * 支持将结构化的过滤规则转换为 OData $filter 查询字符串
 */

import type { FilterOperator, ODataFilterRule } from '../core/types'

/**
 * OData 过滤器构建器
 */
export class ODataFilterBuilder {
  /**
   * 将过滤规则数组转换为 OData $filter 字符串
   */
  static buildFilter(rules: ODataFilterRule[]): string {
    if (!rules || rules.length === 0) {
      return ''
    }

    const filterParts: string[] = []

    for (const [i, rule] of rules.entries()) {
      const filterExpression = this.buildSingleFilter(rule)

      if (i === 0) {
        // 第一个规则不需要逻辑操作符
        filterParts.push(filterExpression)
      } else {
        // 后续规则需要逻辑操作符
        const logical = rule.logical || 'and'
        filterParts.push(`${logical} ${filterExpression}`)
      }
    }

    return filterParts.join(' ')
  }

  /**
   * 构建单个过滤表达式
   */
  private static buildSingleFilter(rule: ODataFilterRule): string {
    const { field, value, operator = 'eq' } = rule

    // 处理字符串值的引号
    const formattedValue = this.formatValue(value, operator)

    // 根据操作符构建表达式
    switch (operator) {
      case 'eq':
        return `${field} eq ${formattedValue}`
      case 'ne':
        return `${field} ne ${formattedValue}`
      case 'gt':
        return `${field} gt ${formattedValue}`
      case 'ge':
        return `${field} ge ${formattedValue}`
      case 'lt':
        return `${field} lt ${formattedValue}`
      case 'le':
        return `${field} le ${formattedValue}`
      case 'contains':
        return `contains(${field}, ${formattedValue})`
      case 'startswith':
        return `startswith(${field}, ${formattedValue})`
      case 'endswith':
        return `endswith(${field}, ${formattedValue})`
      default:
        return `${field} eq ${formattedValue}`
    }
  }

  /**
   * 格式化值
   */
  private static formatValue(
    value: string | number | boolean,
    operator: FilterOperator
  ): string {
    console.log(operator)
    if (typeof value === 'string') {
      // 字符串需要用单引号包围
      return `'${value.replace(/'/g, "''")}'` // 转义单引号
    } else if (typeof value === 'boolean') {
      // 布尔值转为小写
      return value.toString().toLowerCase()
    } else {
      // 数字直接返回
      return value.toString()
    }
  }

  /**
   * 创建简单的等于过滤器
   */
  static eq(field: string, value: string | number | boolean): ODataFilterRule {
    return { field, value, operator: 'eq' }
  }

  /**
   * 创建不等于过滤器
   */
  static ne(field: string, value: string | number | boolean): ODataFilterRule {
    return { field, value, operator: 'ne' }
  }

  /**
   * 创建包含过滤器
   */
  static contains(field: string, value: string): ODataFilterRule {
    return { field, value, operator: 'contains' }
  }

  /**
   * 创建开始于过滤器
   */
  static startsWith(field: string, value: string): ODataFilterRule {
    return { field, value, operator: 'startswith' }
  }

  /**
   * 创建结束于过滤器
   */
  static endsWith(field: string, value: string): ODataFilterRule {
    return { field, value, operator: 'endswith' }
  }

  /**
   * 创建大于过滤器
   */
  static gt(field: string, value: number): ODataFilterRule {
    return { field, value, operator: 'gt' }
  }

  /**
   * 创建大于等于过滤器
   */
  static ge(field: string, value: number): ODataFilterRule {
    return { field, value, operator: 'ge' }
  }

  /**
   * 创建小于过滤器
   */
  static lt(field: string, value: number): ODataFilterRule {
    return { field, value, operator: 'lt' }
  }

  /**
   * 创建小于等于过滤器
   */
  static le(field: string, value: number): ODataFilterRule {
    return { field, value, operator: 'le' }
  }

  /**
   * 创建 AND 逻辑的过滤器
   */
  static and(
    field: string,
    value: string | number | boolean,
    operator: FilterOperator = 'eq'
  ): ODataFilterRule {
    return { field, value, operator, logical: 'and' }
  }

  /**
   * 创建 OR 逻辑的过滤器
   */
  static or(
    field: string,
    value: string | number | boolean,
    operator: FilterOperator = 'eq'
  ): ODataFilterRule {
    return { field, value, operator, logical: 'or' }
  }
}

/**
 * 便捷的过滤器构建函数
 */
export const filter = ODataFilterBuilder

/**
 * 示例用法：
 *
 * // 基本用法
 * const rules = [
 *   filter.eq('Name', 'John'),
 *   filter.or('Email', '<EMAIL>', 'contains')
 * ]
 * const filterString = filter.buildFilter(rules)
 * // 结果: "Name eq 'John' or contains(Email, '<EMAIL>')"
 *
 * // 复杂查询
 * const complexRules = [
 *   filter.contains('FirstName', 'John'),
 *   filter.or('LastName', 'Doe', 'contains'),
 *   filter.and('Age', 18, 'gt'),
 *   filter.and('IsActive', true)
 * ]
 * const complexFilter = filter.buildFilter(complexRules)
 * // 结果: "contains(FirstName, 'John') or contains(LastName, 'Doe') and Age gt 18 and IsActive eq true"
 */
