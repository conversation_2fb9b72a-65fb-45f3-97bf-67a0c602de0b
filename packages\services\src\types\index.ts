/**
 * 简化版服务层类型定义
 */

// 支持的协议类型
export type Protocol = 'rest' | 'odata'

// HTTP 方法类型
export type HttpMethod = 'get' | 'post' | 'put' | 'patch' | 'delete'

// 基础请求配置
export interface BaseRequestConfig {
  url: string
  method: HttpMethod
  headers?: Record<string, string>
  timeout?: number
}

// REST API 配置
export interface RestApiConfig extends BaseRequestConfig {
  protocol?: 'rest'
  params?: Record<string, any>
  body?: any
  dataPath?: string
}

// OData 查询选项
export interface ODataQueryOptions {
  select?: string
  filter?: string
  orderby?: string
  top?: number
  skip?: number
  count?: boolean
  expand?: string
}

// OData API 配置
export interface ODataApiConfig extends BaseRequestConfig {
  protocol: 'odata'
  odata?: ODataQueryOptions
  dataPath?: string
}

// 统一的 API 配置类型
export type ApiConfig = RestApiConfig | ODataApiConfig

// 请求上下文
export interface RequestContext {
  params?: Record<string, any>
  query?: Record<string, any>
  [key: string]: any
}

// 统一的响应格式
export interface ApiResponse<T = any> {
  data: T | T[]
  success: boolean
  total?: number
  error?: string
}

// OData 实体基类
export interface ODataEntity {
  [key: string]: any
}

// OData 服务响应
export interface ODataServiceResponse<T = any> {
  '@odata.context'?: string
  '@odata.count'?: number
  '@odata.nextLink'?: string
  value: T[]
}

// OData 客户端配置
export interface ODataClientConfig {
  baseUrl: string
  headers?: Record<string, string>
  timeout?: number
}

// Axios 客户端配置
export interface AxiosClientConfig {
  baseURL?: string
  headers?: Record<string, string>
  timeout?: number
}

// 客户端接口
export interface ApiClient {
  request<T = any>(config: BaseRequestConfig): Promise<T>
}

// OData 客户端接口
export interface IODataClient {
  query<T = ODataEntity>(
    entitySet: string,
    options?: ODataQueryOptions,
    context?: RequestContext
  ): Promise<ApiResponse<T>>

  queryWithString<T = ODataEntity>(
    entitySet: string,
    queryString: string,
    context?: RequestContext
  ): Promise<ApiResponse<T>>

  get<T = ODataEntity>(
    entitySet: string,
    key: string | number,
    options?: Pick<ODataQueryOptions, 'select' | 'expand'>,
    context?: RequestContext
  ): Promise<ApiResponse<T>>

  create<T = ODataEntity>(
    entitySet: string,
    entity: Partial<T>,
    context?: RequestContext
  ): Promise<ApiResponse<T>>

  update<T = ODataEntity>(
    entitySet: string,
    key: string | number,
    entity: Partial<T>,
    context?: RequestContext
  ): Promise<ApiResponse<T>>

  replace<T = ODataEntity>(
    entitySet: string,
    key: string | number,
    entity: T,
    context?: RequestContext
  ): Promise<ApiResponse<T>>

  delete(
    entitySet: string,
    key: string | number,
    context?: RequestContext
  ): Promise<ApiResponse<boolean>>
}

// 错误类型
export class ServiceError extends Error {
  public status?: number
  public code?: string
  public details?: any

  constructor(message: string, status?: number, code?: string, details?: any) {
    super(message)
    this.name = 'ServiceError'
    this.status = status
    this.code = code
    this.details = details
  }
}
