import { ExtractPropTypes } from 'vue'
import { buildProp } from '@neue-plus/utils'

export interface OptionItem {
  value: string | number | boolean | Record<string, any>
  label?: string | number
  disabled?: boolean
}

export const neSelectProps = {
  placeholder: {
    type: String,
    default: '请选择',
  },
  clearable: {
    type: Boolean,
    default: true,
  },
  options: buildProp({
    type: Array as () => OptionItem[],
    default: () => [],
  }),
}

export type NeSelectProps = ExtractPropTypes<typeof neSelectProps>
