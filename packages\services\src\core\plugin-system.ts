/**
 * 插件系统
 * 提供可扩展的插件架构
 */

import { getGlobalContainer } from './container'
import type {
  ConfigNormalizer,
  Middleware,
  Plugin,
  PluginContext,
  ProtocolAdapter,
} from './interfaces'

/**
 * 插件管理器
 */
export class PluginManager {
  private plugins = new Map<string, Plugin>()
  private adapters = new Map<string, ProtocolAdapter>()
  private middlewares: Middleware[] = []
  private normalizers: ConfigNormalizer[] = []

  /**
   * 安装插件
   */
  async install(plugin: Plugin): Promise<void> {
    if (this.plugins.has(plugin.name)) {
      throw new Error(`Plugin '${plugin.name}' is already installed`)
    }

    const context: PluginContext = {
      registerAdapter: (adapter) => {
        this.adapters.set(adapter.protocol, adapter)
      },
      registerMiddleware: (middleware) => {
        this.middlewares.push(middleware)
      },
      registerNormalizer: (normalizer) => {
        this.normalizers.push(normalizer)
      },
      getService: (name) => {
        const container = getGlobalContainer()
        return container.has(name) ? container.get(name) : undefined
      },
      registerService: (name, service) => {
        const container = getGlobalContainer()
        container.register(name, () => service)
      },
    }

    await plugin.install(context)
    this.plugins.set(plugin.name, plugin)
  }

  /**
   * 卸载插件
   */
  async uninstall(pluginName: string): Promise<void> {
    const plugin = this.plugins.get(pluginName)
    if (!plugin) {
      throw new Error(`Plugin '${pluginName}' is not installed`)
    }

    if (plugin.uninstall) {
      await plugin.uninstall()
    }

    this.plugins.delete(pluginName)
  }

  /**
   * 获取已安装的插件
   */
  getInstalledPlugins(): Plugin[] {
    return Array.from(this.plugins.values())
  }

  /**
   * 获取注册的适配器
   */
  getAdapters(): ProtocolAdapter[] {
    return Array.from(this.adapters.values())
  }

  /**
   * 获取注册的中间件
   */
  getMiddlewares(): Middleware[] {
    return [...this.middlewares]
  }

  /**
   * 获取注册的标准化器
   */
  getNormalizers(): ConfigNormalizer[] {
    return [...this.normalizers]
  }

  /**
   * 检查插件是否已安装
   */
  isInstalled(pluginName: string): boolean {
    return this.plugins.has(pluginName)
  }

  /**
   * 清除所有插件
   */
  async clear(): Promise<void> {
    const uninstallPromises = Array.from(this.plugins.keys()).map((name) =>
      this.uninstall(name)
    )
    await Promise.all(uninstallPromises)
  }
}

// 全局插件管理器
let globalPluginManager: PluginManager | null = null

/**
 * 获取全局插件管理器
 */
export function getGlobalPluginManager(): PluginManager {
  if (!globalPluginManager) {
    globalPluginManager = new PluginManager()
  }
  return globalPluginManager
}

/**
 * 设置全局插件管理器
 */
export function setGlobalPluginManager(manager: PluginManager): void {
  globalPluginManager = manager
}

/**
 * 创建插件管理器
 */
export function createPluginManager(): PluginManager {
  return new PluginManager()
}

/**
 * 便捷的插件安装函数
 */
export async function installPlugin(plugin: Plugin): Promise<void> {
  const manager = getGlobalPluginManager()
  await manager.install(plugin)
}

/**
 * 便捷的插件卸载函数
 */
export async function uninstallPlugin(pluginName: string): Promise<void> {
  const manager = getGlobalPluginManager()
  await manager.uninstall(pluginName)
}

/**
 * 创建简单插件的辅助函数
 */
export function createPlugin(
  name: string,
  install: (context: PluginContext) => void | Promise<void>,
  uninstall?: () => void | Promise<void>
): Plugin {
  return {
    name,
    install,
    uninstall,
  }
}

/**
 * 创建适配器插件
 */
export function createAdapterPlugin(
  name: string,
  adapter: ProtocolAdapter
): Plugin {
  return createPlugin(name, (context) => {
    context.registerAdapter(adapter)
  })
}

/**
 * 创建中间件插件
 */
export function createMiddlewarePlugin(
  name: string,
  middleware: Middleware
): Plugin {
  return createPlugin(name, (context) => {
    context.registerMiddleware(middleware)
  })
}

/**
 * 创建标准化器插件
 */
export function createNormalizerPlugin(
  name: string,
  normalizer: ConfigNormalizer
): Plugin {
  return createPlugin(name, (context) => {
    context.registerNormalizer(normalizer)
  })
}

/**
 * 批量安装插件
 */
export async function installPlugins(plugins: Plugin[]): Promise<void> {
  const manager = getGlobalPluginManager()
  for (const plugin of plugins) {
    await manager.install(plugin)
  }
}

/**
 * 从配置安装插件
 */
export async function installPluginsFromConfig(config: {
  plugins: Array<{
    name: string
    type: 'adapter' | 'middleware' | 'normalizer'
    implementation: any
  }>
}): Promise<void> {
  const plugins: Plugin[] = config.plugins.map((pluginConfig) => {
    switch (pluginConfig.type) {
      case 'adapter':
        return createAdapterPlugin(
          pluginConfig.name,
          pluginConfig.implementation
        )
      case 'middleware':
        return createMiddlewarePlugin(
          pluginConfig.name,
          pluginConfig.implementation
        )
      case 'normalizer':
        return createNormalizerPlugin(
          pluginConfig.name,
          pluginConfig.implementation
        )
      default:
        throw new Error(`Unknown plugin type: ${pluginConfig.type}`)
    }
  })

  await installPlugins(plugins)
}
