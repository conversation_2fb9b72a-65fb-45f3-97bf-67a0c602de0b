import { epPackage, getPackageDependencies } from '@neue-plus/build-utils'

import type { OutputOptions, RollupBuild } from 'rollup'

export const generateExternal = async (options: { full: boolean }) => {
  const { dependencies, peerDependencies } = getPackageDependencies(epPackage)

  // Node.js 内置模块列表 - 这些模块不应该出现在浏览器构建中
  const nodeBuiltins = [
    'util',
    'stream',
    'path',
    'http',
    'https',
    'url',
    'fs',
    'fs/promises',
    'assert',
    'tty',
    'zlib',
    'events',
    'os',
    'child_process',
    'crypto',
    'buffer',
    'net',
    'dgram',
    'dns',
    'readline',
    'repl',
    'tls',
    'cluster',
    'worker_threads',
    'perf_hooks',
    'async_hooks',
    'inspector',
    'process',
    'module',
    'fsevents', // macOS 文件系统监听，在其他平台不需要
  ]

  return (id: string) => {
    // 对于所有构建，都排除 Node.js 内置模块（它们不应该被打包到浏览器库中）
    if (nodeBuiltins.includes(id)) {
      return true
    }

    // 处理 Node.js 内置模块的 node: 前缀形式
    if (id.startsWith('node:')) {
      const moduleName = id.slice(5) // 移除 'node:' 前缀
      if (nodeBuiltins.includes(moduleName)) {
        return true
      }
    }

    // 处理 CommonJS 外部模块（带有 ?commonjs-external 后缀）
    if (id.includes('?commonjs-external')) {
      const cleanId = id.split('?')[0]
      if (nodeBuiltins.includes(cleanId) || cleanId.startsWith('node:')) {
        return true
      }
    }

    // 特别处理 axios - 确保它及其所有子模块都被外部化
    if (id === 'axios' || id.startsWith('axios/')) {
      return true
    }

    // 排除 fsevents 相关的所有模块
    if (id.includes('fsevents')) {
      return true
    }

    const packages: string[] = [...peerDependencies]
    if (!options.full) {
      packages.push('@vue', ...dependencies)
    }

    return [...new Set(packages)].some(
      (pkg) => id === pkg || id.startsWith(`${pkg}/`)
    )
  }
}

export function writeBundles(bundle: RollupBuild, options: OutputOptions[]) {
  return Promise.all(options.map((option) => bundle.write(option)))
}

export function formatBundleFilename(
  name: string,
  minify: boolean,
  ext: string
) {
  return `${name}${minify ? '.min' : ''}.${ext}`
}
