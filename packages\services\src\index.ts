/**
 * 简化版服务层框架
 * 支持 REST 和 OData 协议的统一 API 调用接口
 */

// 导出所有类型定义
export type {
  Protocol,
  HttpMethod,
  BaseRequestConfig,
  RestApiConfig,
  ODataApiConfig,
  ApiConfig,
  RequestContext,
  ApiResponse,
  ODataEntity,
  ODataServiceResponse,
  ODataQueryOptions,
  ODataClientConfig,
  AxiosClientConfig,
  ApiClient,
  IODataClient,
} from './types'

// 导出错误类
export { ServiceError } from './types'

// 导出 Axios 客户端
export {
  AxiosClient,
  createAxiosClient,
  getDefaultAxiosClient,
  setDefaultAxiosClient,
} from './clients/axios-client'

// 导出 OData 客户端
export {
  ODataClient,
  createODataClient,
  getDefaultODataClient,
  setDefaultODataClient,
} from './clients/odata-client'

// 导出工具函数
export {
  executeApi,
  isODataConfig,
  isRestConfig,
  createSuccessResponse,
  createErrorResponse,
  formatUrl,
  mergeHeaders,
  delay,
  retry,
} from './utils'

// 默认导出 executeApi 函数
export { executeApi as default } from './utils'
