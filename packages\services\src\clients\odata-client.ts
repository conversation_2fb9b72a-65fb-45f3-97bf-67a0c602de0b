/**
 * 简化版 OData 客户端实现
 */

import {
  ApiResponse,
  IODataClient,
  ODataClientConfig,
  ODataEntity,
  ODataQueryOptions,
  ODataServiceResponse,
  RequestContext,
  ServiceError,
} from '../types'
import { AxiosClient } from './axios-client'

export class ODataClient implements IODataClient {
  private axiosClient: AxiosClient
  private config: ODataClientConfig

  constructor(config: ODataClientConfig) {
    this.config = config
    this.axiosClient = new AxiosClient({
      baseURL: config.baseUrl,
      timeout: config.timeout,
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        ...config.headers,
      },
    })
  }

  async query<T = ODataEntity>(
    entitySet: string,
    options: ODataQueryOptions = {},
    context: RequestContext = {}
  ): Promise<ApiResponse<T>> {
    try {
      const url = this.buildUrl(entitySet)
      const params = this.buildQueryParams(options)

      console.log(`[OData] 查询实体集: ${entitySet}`, { options, params })

      const response = await this.axiosClient.get<ODataServiceResponse<T>>(
        url,
        params
      )

      return this.formatResponse(response, options.count)
    } catch (error) {
      throw this.handleError(error, `查询实体集 ${entitySet} 失败`)
    }
  }

  async get<T = ODataEntity>(
    entitySet: string,
    key: string | number,
    options: Pick<ODataQueryOptions, 'select' | 'expand'> = {},
    context: RequestContext = {}
  ): Promise<ApiResponse<T>> {
    try {
      const url = this.buildUrl(entitySet, key)
      const params = this.buildQueryParams(options)

      console.log(`[OData] 获取实体: ${entitySet}(${key})`, { options, params })

      const response = await this.axiosClient.get<T>(url, params)

      return {
        data: response,
        success: true,
      }
    } catch (error) {
      throw this.handleError(error, `获取实体 ${entitySet}(${key}) 失败`)
    }
  }

  async create<T = ODataEntity>(
    entitySet: string,
    entity: Partial<T>,
    context: RequestContext = {}
  ): Promise<ApiResponse<T>> {
    try {
      const url = this.buildUrl(entitySet)

      console.log(`[OData] 创建实体: ${entitySet}`, entity)

      const response = await this.axiosClient.post<T>(url, entity)

      return {
        data: response,
        success: true,
      }
    } catch (error) {
      throw this.handleError(error, `创建实体 ${entitySet} 失败`)
    }
  }

  async update<T = ODataEntity>(
    entitySet: string,
    key: string | number,
    entity: Partial<T>,
    context: RequestContext = {}
  ): Promise<ApiResponse<T>> {
    try {
      const url = this.buildUrl(entitySet, key)

      console.log(`[OData] 更新实体: ${entitySet}(${key})`, entity)

      const response = await this.axiosClient.patch<T>(url, entity)

      return {
        data: response,
        success: true,
      }
    } catch (error) {
      throw this.handleError(error, `更新实体 ${entitySet}(${key}) 失败`)
    }
  }

  async delete(
    entitySet: string,
    key: string | number,
    context: RequestContext = {}
  ): Promise<ApiResponse<boolean>> {
    try {
      const url = this.buildUrl(entitySet, key)

      console.log(`[OData] 删除实体: ${entitySet}(${key})`)

      await this.axiosClient.delete(url)

      return {
        data: true,
        success: true,
      }
    } catch (error) {
      throw this.handleError(error, `删除实体 ${entitySet}(${key}) 失败`)
    }
  }

  private buildUrl(entitySet: string, key?: string | number): string {
    let url = `/${entitySet}`

    if (key !== undefined) {
      // 处理字符串键（需要引号）和数字键
      const keyStr = typeof key === 'string' ? `'${key}'` : key.toString()
      url += `(${keyStr})`
    }

    return url
  }

  private buildQueryParams(
    options: Partial<ODataQueryOptions>
  ): Record<string, any> {
    const params: Record<string, any> = {}

    if (options.select) {
      params.$select = options.select
    }

    if (options.filter) {
      params.$filter = options.filter
    }

    if (options.orderby) {
      params.$orderby = options.orderby
    }

    if (options.top !== undefined) {
      params.$top = options.top
    }

    if (options.skip !== undefined) {
      params.$skip = options.skip
    }

    if (options.count) {
      params.$count = true
    }

    if (options.expand) {
      params.$expand = options.expand
    }

    return params
  }

  private formatResponse<T>(
    response: ODataServiceResponse<T>,
    includeCount?: boolean
  ): ApiResponse<T> {
    const result: ApiResponse<T> = {
      data: response.value || (response as any),
      success: true,
    }

    if (includeCount && response['@odata.count'] !== undefined) {
      result.total = response['@odata.count']
    }

    return result
  }

  private handleError(error: any, message: string): ServiceError {
    if (error instanceof ServiceError) {
      return error
    }

    return new ServiceError(
      `${message}: ${error.message || '未知错误'}`,
      error.status,
      'ODATA_ERROR',
      error
    )
  }
}

// 创建默认的 OData 客户端实例
let defaultODataClient: ODataClient | null = null

export function createODataClient(config: ODataClientConfig): ODataClient {
  return new ODataClient(config)
}

export function getDefaultODataClient(): ODataClient | null {
  return defaultODataClient
}

export function setDefaultODataClient(client: ODataClient): void {
  defaultODataClient = client
}
