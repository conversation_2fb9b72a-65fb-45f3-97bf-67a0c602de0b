import { computed, defineComponent, inject, resolveDynamicComponent } from 'vue'
import { keys, merge } from 'lodash-unified'
import { getComponentByName } from '../utils'
import { ElementEvent, executeApi } from '../../types'
import { APIS_KEY, REFS_KEY } from '../context/event-flow'
import { useEventFlow } from '../hooks/useEventFlow'
import { neRenderCoreProps } from './types'

export default defineComponent({
  name: 'NeRenderCore',
  props: neRenderCoreProps,
  setup(props) {
    const eventFlow = useEventFlow()
    const refs = inject(REFS_KEY, {}) as Record<string, any>
    const apiRefs = inject(APIS_KEY, {}) as Record<string, any>
    const elProps = computed(() => props.props || {})
    const elements = computed(() => props.elements || [])
    const resolvedComponent = computed(() => {
      return getComponentByName(props.type)
    })
    const dynamicEvents = computed(() => {
      const result: Record<string, (...args: any[]) => void> = {}
      props.events.forEach((evt: ElementEvent) => {
        if (evt.eventName && Array.isArray(evt.actions)) {
          result[evt.eventName] = (arg0, arg1, arg2) => {
            eventFlow?.run(evt.actions, {
              id: props.id,
              clickParams: [arg0, arg1, arg2],
            })
          }
        }
      })
      return result
    })
    const dynamicApiEvents = computed(() => {
      const result: Record<string, (...args: any[]) => void> = {}
      const allApis = { ...apiRefs, ...props.apis }
      keys(allApis).forEach((item: string) => {
        result[item] = async ({ params, context, query }) => {
          console.log(merge({}, allApis[item], { params, query }))
          return executeApi(
            merge({}, allApis[item], { params, query }),
            context
          )
        }
      })
      return result
    })
    const setRef = (el: any) => {
      if (props.id && el) {
        refs[props.id] = el
      }
    }

    return () => {
      const DynamicTag = resolveDynamicComponent(resolvedComponent.value) as any
      const NeRenderCore = resolveDynamicComponent('NeRenderCore') as any
      const defaultSlot = elements.value?.length
        ? () =>
            elements.value.map((child, index) => (
              <NeRenderCore {...child} key={index} />
            ))
        : undefined
      const dynamicSlots = props.slots
        ? Object.fromEntries(
            Object.entries(props.slots).map(([slotName, slotValue]) => [
              slotName,
              () =>
                Array.isArray(slotValue)
                  ? slotValue.map((child: any, index: number) => (
                      <NeRenderCore {...child} key={index} />
                    ))
                  : slotValue,
            ])
          )
        : {}
      console.log(dynamicApiEvents.value, 'dynamicApiEvents')
      return (
        <DynamicTag
          ref={setRef}
          {...elProps.value}
          {...dynamicEvents.value}
          {...dynamicApiEvents.value}
        >
          {{
            default: defaultSlot,
            ...dynamicSlots,
          }}
        </DynamicTag>
      )
    }
  },
})
