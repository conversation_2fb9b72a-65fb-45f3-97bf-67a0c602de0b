<template>
  <div class="api-request-demo">
    <el-card class="demo-header">
      <template #header>
        <div class="header-content">
          <h2>🚀 API 请求组件演示</h2>
          <el-tag type="primary">支持 5 种 HTTP 方法</el-tag>
        </div>
      </template>

      <div class="demo-description">
        <p>
          这个组件支持 GET、POST、PUT、PATCH、DELETE 五种 HTTP 方法，可以测试
          REST 和 OData 两种协议。
        </p>
        <el-alert title="使用提示" type="info" :closable="false" show-icon>
          <ul>
            <li>选择 HTTP 方法和协议类型</li>
            <li>输入 API 地址</li>
            <li>配置请求头、参数和请求体</li>
            <li>点击"发送请求"按钮执行</li>
            <li>查看响应结果和性能数据</li>
          </ul>
        </el-alert>
      </div>
    </el-card>

    <!-- API 请求组件 -->
    <ApiRequestButton />

    <!-- 快速示例 -->
    <el-card class="quick-examples">
      <template #header>
        <h3>⚡ 快速示例</h3>
      </template>

      <el-row :gutter="16">
        <el-col :span="8">
          <el-card class="example-card" shadow="hover">
            <h4>📖 GET 请求示例</h4>
            <p>获取用户列表</p>
            <el-button size="small" @click="loadGetExample">加载示例</el-button>
            <div class="example-code">
              <pre>
GET /posts?_limit=5
Protocol: REST</pre
              >
            </div>
          </el-card>
        </el-col>

        <el-col :span="8">
          <el-card class="example-card" shadow="hover">
            <h4>📝 POST 请求示例</h4>
            <p>创建新文章</p>
            <el-button size="small" @click="loadPostExample"
              >加载示例</el-button
            >
            <div class="example-code">
              <pre>
POST /posts
Body: { title, body, userId }</pre
              >
            </div>
          </el-card>
        </el-col>

        <el-col :span="8">
          <el-card class="example-card" shadow="hover">
            <h4>🔍 OData 查询示例</h4>
            <p>查询用户信息</p>
            <el-button size="small" @click="loadODataExample"
              >加载示例</el-button
            >
            <div class="example-code">
              <pre>
GET /People
$select: FirstName,LastName
$top: 5</pre
              >
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>

    <!-- 功能特性 -->
    <el-card class="features">
      <template #header>
        <h3>✨ 功能特性</h3>
      </template>

      <el-row :gutter="16">
        <el-col :span="12">
          <div class="feature-list">
            <h4>🔧 请求配置</h4>
            <ul>
              <li>支持 5 种 HTTP 方法（GET、POST、PUT、PATCH、DELETE）</li>
              <li>支持 REST 和 OData 协议</li>
              <li>自定义请求头和查询参数</li>
              <li>JSON 格式的请求体</li>
              <li>OData 查询选项（$select、$filter、$top 等）</li>
            </ul>
          </div>
        </el-col>

        <el-col :span="12">
          <div class="feature-list">
            <h4>📊 响应处理</h4>
            <ul>
              <li>格式化显示响应数据</li>
              <li>请求性能监控（响应时间）</li>
              <li>错误信息展示</li>
              <li>数据统计信息</li>
              <li>可折叠的响应数据查看</li>
            </ul>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 使用技巧 -->
    <el-card class="tips">
      <template #header>
        <h3>💡 使用技巧</h3>
      </template>

      <el-timeline>
        <el-timeline-item timestamp="步骤 1" placement="top">
          <el-card>
            <h4>选择请求方法</h4>
            <p>
              根据操作类型选择合适的 HTTP
              方法：GET（查询）、POST（创建）、PUT（完整更新）、PATCH（部分更新）、DELETE（删除）
            </p>
          </el-card>
        </el-timeline-item>

        <el-timeline-item timestamp="步骤 2" placement="top">
          <el-card>
            <h4>配置请求参数</h4>
            <p>
              在对应的标签页中配置 Headers、Query Params、Body 或 OData
              选项。可以使用"加载示例"快速开始。
            </p>
          </el-card>
        </el-timeline-item>

        <el-timeline-item timestamp="步骤 3" placement="top">
          <el-card>
            <h4>发送请求并查看结果</h4>
            <p>
              点击"发送请求"按钮执行 API
              调用，查看响应数据、性能指标和可能的错误信息。
            </p>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import ApiRequestButton from './ApiRequestButton.vue'

// 示例加载方法
const loadGetExample = () => {
  ElMessage.info('请在上方组件中点击"加载示例"按钮，然后选择 GET 方法')
}

const loadPostExample = () => {
  ElMessage.info('请在上方组件中选择 POST 方法，然后点击"加载示例"按钮')
}

const loadODataExample = () => {
  ElMessage.info('请在上方组件中选择 OData 协议，然后点击"加载示例"按钮')
}
</script>

<style scoped>
.api-request-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content h2 {
  margin: 0;
  color: #303133;
}

.demo-description {
  color: #606266;
}

.demo-description p {
  margin-bottom: 16px;
  font-size: 16px;
}

.demo-description ul {
  margin: 0;
  padding-left: 20px;
}

.demo-description li {
  margin-bottom: 4px;
}

.quick-examples {
  margin: 20px 0;
}

.example-card {
  height: 200px;
  text-align: center;
}

.example-card h4 {
  color: #409eff;
  margin-bottom: 8px;
}

.example-card p {
  color: #909399;
  margin-bottom: 12px;
}

.example-code {
  margin-top: 12px;
  text-align: left;
}

.example-code pre {
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 8px;
  font-size: 12px;
  color: #2c3e50;
  margin: 0;
}

.features {
  margin: 20px 0;
}

.feature-list h4 {
  color: #303133;
  margin-bottom: 12px;
}

.feature-list ul {
  margin: 0;
  padding-left: 20px;
}

.feature-list li {
  margin-bottom: 8px;
  color: #606266;
}

.tips {
  margin: 20px 0;
}

.el-timeline-item .el-card {
  margin-top: 0;
}

.el-timeline-item .el-card h4 {
  color: #409eff;
  margin-bottom: 8px;
}

.el-timeline-item .el-card p {
  color: #606266;
  margin: 0;
  line-height: 1.6;
}
</style>
