import { defineComponent, ref } from 'vue'
import { ElDrawer } from 'element-plus'
import { neDrawerProps } from './types'

const NeDrawer = defineComponent({
  name: 'NeDrawer',
  props: neDrawerProps,
  setup(props, { slots, expose }) {
    expose({
      open: () => {
        visible.value = true
      },
      close: () => {
        visible.value = false
      },
    })
    const visible = ref(props.modelValue || false)
    return () => (
      <ElDrawer {...props} v-model={visible.value}>
        {{
          ...slots,
        }}
      </ElDrawer>
    )
  },
})

export default NeDrawer
