<template>
  <widget-wrapper>
    <NeProTable :columns="columns" :data="data" @row-click="rowClick" />
  </widget-wrapper>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import NeProTable from '@neue-plus/components/pro-table'
import WidgetWrapper from '@neue-plus/components/widget-wrapper'
import { useApiRefs } from '@neue-plus/components/material-render/src/context/event-flow'
import { widgetTableProps } from './type'

defineOptions({
  name: 'WidgetTable',
  inheritAttrs: false,
})
defineProps({ ...widgetTableProps })
const columns = ref([])
const data = ref([])
const emit = defineEmits(['rowClick'])

function rowClick(row: any) {
  emit('rowClick', row)
}

const { apis } = useApiRefs()

onMounted(() => {
  console.log(apis)
})
defineExpose({
  describe: 'describe', //保留字段
})
</script>
