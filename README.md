# 框架部分

- [x] 已完成核心代码 ui 渲染器
- [x] 事件流程规范制定与实现
- [ ] 业务接口用的 api 封装和 odata-api 封装
- [ ] 设计器

# 组件部分

- [x] button 组件
- [x] card 组件
- [x] checkbox group 组件
- [x] drawer 组件
- [x] table 组件
- [x] protable 组件
- [x] radio group 组件
- [x] config-provider 组件
- [x] tree-table 组件
- [ ] cascader 树形选择组件
- [ ] select 选择组件
- [ ] input 输入框
- [ ] input-number 数字输入框
- [ ] date-picker 日期选择框
- [ ] time-picker 时间选择框
- [ ] time-range-picker 时间范围选择框
- [ ] date-range-picker 日期范围选择框
- [ ] upload 上传组件
- [ ] transfer 穿梭框
- [ ] slider 滑动输入条
- [ ] rate 评分
- [ ] tree 树形控件
- [ ] 其他组件

# 其他工作

按照 UI 设计稿修改组件样式
