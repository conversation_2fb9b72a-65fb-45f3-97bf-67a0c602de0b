import { computed, defineComponent } from 'vue'
import { ElOption, ElSelect } from 'element-plus'
import { neSelectProps } from './types'

const NeSelect = defineComponent({
  name: 'NeSelect',
  props: neSelectProps,
  setup(props) {
    const { options, ...restProps } = props
    const realProps = computed(() => {
      return {
        ...restProps,
        placeholder: props.placeholder || '请选择',
      }
    })
    return () => (
      <ElSelect {...realProps.value}>
        {options.map((item) => {
          return <ElOption {...item} />
        })}
      </ElSelect>
    )
  },
})

export default NeSelect
