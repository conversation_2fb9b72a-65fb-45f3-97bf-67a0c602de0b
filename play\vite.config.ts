import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import Inspect from 'vite-plugin-inspect'
import VueMacros from 'unplugin-vue-macros/vite'
import { defineConfig } from 'vite'
// import { resolve } from 'path'
import Components from 'unplugin-vue-components/vite'
import { NeuePlusResolver } from '@neue-plus/resolver'
export default defineConfig({
  build: {
    sourcemap: true,
  },
  plugins: [
    VueMacros({
      setupComponent: false,
      setupSFC: false,
      plugins: {
        vue: vue(),
        vueJsx: vueJsx(),
      },
    }),
    // AutoImport({
    //   resolvers: [ElementPlusResolver()],
    // }),
    Components({
      include: `${__dirname}/**`,
      resolvers: [
        NeuePlusResolver({
          version: '1.0.0',
          importStyle: 'sass',
        }),
      ],
      dts: false,
    }),
    Inspect(),
  ],
  resolve: {
    alias: {
      vue: 'vue/dist/vue.esm-bundler.js', // ✅ 关键配置
      // 'neue-plus': resolve(__dirname, '../packages/neue-plus/index.ts'),
    },
  },
})
